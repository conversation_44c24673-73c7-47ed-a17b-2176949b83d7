# 📚 Markdown 轉換工具使用說明

## 🎯 工具概述

這是一個專為教育材料設計的 Markdown 到 HTML 轉換工具，支援數學公式、圖片、表格等完整功能。

### ✅ 主要特色

- **🔢 數學公式支援**：完整支援 LaTeX 格式的數學公式（MathJax 3.0）
- **🖼️ 圖片處理**：自動處理相對路徑圖片，支援多種格式
- **📊 表格功能**：完整的表格支援，包含樣式和對齊
- **✅ 教育專用功能**：Checkbox、填空線、回答線等
- **🎨 美觀樣式**：專業的 CSS 樣式，適合教育材料
- **⚡ 簡單易用**：一個命令完成轉換，無需 Web 介面

## 🚀 快速開始

### 基本使用

```bash
# 轉換單個 Markdown 文件
python convert_markdown.py document.md

# 指定輸出文件名
python convert_markdown.py document.md -o output.html

# 顯示詳細信息
python convert_markdown.py document.md -v
```

### 實際範例

```bash
# 轉換測試文件
python convert_markdown.py TestFile/comprehensive_test.md

# 轉換數學公式文件
python convert_markdown.py TestFile/simple_math_test.md

# 轉換並指定輸出位置
python convert_markdown.py TestFile/comprehensive_test.md -o output/result.html
```

## 📋 支援的功能

### ✅ 基礎 Markdown 功能
- 標題層級 (H1-H6)
- 文字格式（粗體、斜體、刪除線）
- 段落和換行
- 引用區塊
- 代碼區塊和行內代碼
- 連結和圖片
- 分隔線

### ✅ 列表功能
- 無序列表（支援多層嵌套）
- 有序列表（支援多層嵌套）
- 任務列表（Checkbox）：`- [x]` 和 `- [ ]`

### ✅ 表格功能
- 基本表格語法
- 表格對齊（左對齊、居中、右對齊）
- 自動樣式（邊框、背景色、懸停效果）

### ✅ 數學公式功能
- **行內公式**：`$E = mc^2$`
- **行中公式**：`$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$`
- **複雜公式**：支援所有 LaTeX 數學符號
- **自動渲染**：使用 MathJax 3.0 引擎

### ✅ 圖片功能
- 基本圖片：`![描述](path/to/image.png)`
- 相對路徑自動解析
- 支援格式：PNG, JPG, JPEG, GIF, SVG
- 響應式設計（自動縮放）

### ✅ 教育專用功能
- **填空線**：`____` 自動轉換為填空線
- **回答題橫線**：`---` 轉換為回答區域
- **Checkbox 任務**：完整支援任務列表

## 📁 文件結構

```
MD_Convert/
├── convert_markdown.py          # 主轉換程式
├── backend/
│   ├── core/
│   │   ├── markdown_parser.py   # Markdown 解析器
│   │   └── document_processor.py # 文檔處理器
├── TestFile/                    # 測試文件
│   ├── comprehensive_test.md    # 綜合測試文件
│   ├── simple_math_test.md      # 數學公式測試
│   └── photo/                   # 測試圖片
└── README_轉換工具使用說明.md    # 本說明文件
```

## 🔧 技術細節

### 核心組件
- **解析引擎**：markdown-it-py
- **數學公式**：MathJax 3.0
- **樣式系統**：內建 CSS（GitHub 風格）
- **圖片處理**：自動路徑解析和 base64 編碼

### 輸出特色
- **完整 HTML**：包含所有必要的 CSS 和 JavaScript
- **離線可用**：生成的 HTML 文件可離線查看
- **響應式設計**：適配不同螢幕尺寸
- **列印友好**：適合列印的樣式

## 📊 使用範例

### 數學公式範例

**輸入 Markdown：**
```markdown
# 數學公式測試

行內公式：愛因斯坦公式 $E = mc^2$

行中公式：
$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$
```

**輸出效果：**
- 行內公式會在文字中正確顯示
- 行中公式會居中顯示，格式美觀
- 所有數學符號正確渲染

### 表格範例

**輸入 Markdown：**
```markdown
| 科目 | 分數 | 等級 |
|------|------|------|
| 數學 | 95   | A+   |
| 物理 | 88   | A    |
| 化學 | 92   | A+   |
```

**輸出效果：**
- 自動添加邊框和背景色
- 表頭有特殊樣式
- 懸停效果增強可讀性

### 任務列表範例

**輸入 Markdown：**
```markdown
## 學習進度

- [x] 完成第一章
- [x] 做完練習題
- [ ] 準備期中考試
- [ ] 複習重點內容
```

**輸出效果：**
- 已完成項目顯示勾選框
- 未完成項目顯示空白框
- 清晰的視覺區分

## ⚠️ 注意事項

### 文件編碼
- 請確保 Markdown 文件使用 UTF-8 編碼
- 如果遇到編碼問題，工具會自動嘗試 GBK 編碼

### 圖片路徑
- 建議使用相對路徑引用圖片
- 圖片文件應與 Markdown 文件在同一項目目錄下
- 支援子目錄結構（如 `photo/image.png`）

### 數學公式
- 使用標準 LaTeX 語法
- 行內公式用單個 `$` 包圍
- 行中公式用雙個 `$$` 包圍
- 複雜公式建議使用行中模式

## 🔍 故障排除

### 常見問題

**Q: 數學公式不顯示？**
A: 檢查網路連接，MathJax 需要從 CDN 載入。

**Q: 圖片不顯示？**
A: 確認圖片路徑正確，且圖片文件存在。

**Q: 中文顯示亂碼？**
A: 確保 Markdown 文件使用 UTF-8 編碼保存。

**Q: 轉換失敗？**
A: 使用 `-v` 參數查看詳細錯誤信息。

### 獲取幫助

```bash
# 查看幫助信息
python convert_markdown.py -h

# 查看詳細處理過程
python convert_markdown.py document.md -v
```

## 🎉 成功案例

### 已測試的功能
- ✅ 複雜數學公式（微積分、線性代數、統計學）
- ✅ 多層嵌套列表
- ✅ 複雜表格結構
- ✅ 圖片和文字混合排版
- ✅ 教育材料專用格式
- ✅ 中文內容完美支援

### 性能表現
- **轉換速度**：大型文件（4000+ 字符）< 1 秒
- **文件大小**：包含圖片的 HTML 文件自動優化
- **兼容性**：生成的 HTML 支援所有現代瀏覽器

---

**🔧 開發團隊**：教育材料轉換系統 Week 1  
**📅 更新時間**：2024年6月  
**🎯 版本**：v1.0 - 直接轉換版本

#!/usr/bin/env python3
"""
Markdown 到 HTML 直接轉換工具
支援數學公式、圖片、表格等完整功能

使用方法：
    python convert_markdown.py input.md
    python convert_markdown.py input.md -o output.html
    python convert_markdown.py project/document.md
"""

import os
import sys
import argparse
import base64
from pathlib import Path

# 添加 backend 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'backend'))

from core.markdown_parser import MarkdownParser
from core.document_processor import DocumentProcessor


class LocalMarkdownConverter:
    """本地 Markdown 轉換器"""
    
    def __init__(self):
        self.parser = MarkdownParser()
        self.processor = DocumentProcessor()
    
    def convert_file(self, input_path: str, output_path: str = None) -> str:
        """
        轉換 Markdown 文件為 HTML
        
        Args:
            input_path: 輸入的 Markdown 文件路徑
            output_path: 輸出的 HTML 文件路徑（可選）
            
        Returns:
            生成的 HTML 內容
        """
        # 驗證輸入文件
        if not os.path.exists(input_path):
            raise FileNotFoundError(f"找不到文件: {input_path}")
        
        if not input_path.endswith(('.md', '.markdown', '.txt')):
            raise ValueError("請提供 Markdown 文件 (.md, .markdown, .txt)")
        
        # 讀取文件內容
        try:
            with open(input_path, 'r', encoding='utf-8') as f:
                markdown_content = f.read()
        except UnicodeDecodeError:
            print("⚠️ UTF-8 解碼失敗，嘗試使用其他編碼...")
            with open(input_path, 'r', encoding='gbk') as f:
                markdown_content = f.read()
        
        # 獲取文件的基礎路徑（用於圖片路徑解析）
        base_path = os.path.dirname(os.path.abspath(input_path))
        
        print(f"📄 讀取文件: {input_path}")
        print(f"📁 基礎路徑: {base_path}")
        print(f"📊 文件大小: {len(markdown_content):,} 字符")
        
        # 使用現有的處理器轉換
        try:
            result = self.processor.process_document(
                markdown_content=markdown_content,
                options={
                    'output_formats': ['html'],
                    'theme': 'default'
                },
                base_path=base_path
            )
            
            if not result.get('success'):
                raise Exception(f"轉換失敗: {result.get('errors', ['未知錯誤'])}")
            
            html_content = result['html_content']
            
            # 生成完整的 HTML 文檔
            complete_html = self._generate_complete_html(
                content=html_content,
                title=os.path.basename(input_path),
                metadata=result.get('metadata', {})
            )
            
            # 輸出文件
            if output_path is None:
                output_path = os.path.splitext(input_path)[0] + '.html'
            
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(complete_html)
            
            print(f"✅ 轉換完成!")
            print(f"📄 輸出文件: {output_path}")
            print(f"📊 處理統計: {result['metadata']}")
            
            return complete_html
            
        except Exception as e:
            print(f"❌ 轉換失敗: {e}")
            raise
    
    def _generate_complete_html(self, content: str, title: str, metadata: dict) -> str:
        """生成完整的 HTML 文檔"""
        
        # 檢查是否包含數學公式
        has_math = metadata.get('math_formulas_found', 0) > 0
        
        # MathJax 配置（如果需要）
        mathjax_config = ""
        if has_math:
            mathjax_config = '''
    <!-- MathJax 3.0 配置 -->
    <script>
    window.MathJax = {
      tex: {
        inlineMath: [['$', '$']],
        displayMath: [['$$', '$$']],
        processEscapes: true,
        processEnvironments: true
      },
      startup: {
        ready: () => {
          console.log('🔢 MathJax 3.0 正在初始化...');
          MathJax.startup.defaultReady();
        },
        pageReady: () => {
          console.log('🔢 MathJax 頁面準備完成');
          return MathJax.startup.defaultPageReady().then(() => {
            console.log('🔢 MathJax 渲染完成！');
          });
        }
      }
    };
    </script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
'''
        
        # 完整的 HTML 模板
        html_template = f'''<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{title}</title>
    <style>
        body {{
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }}
        
        h1, h2, h3, h4, h5, h6 {{
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }}
        
        h1 {{ font-size: 2em; border-bottom: 1px solid #eaecef; padding-bottom: 10px; }}
        h2 {{ font-size: 1.5em; border-bottom: 1px solid #eaecef; padding-bottom: 8px; }}
        h3 {{ font-size: 1.25em; }}
        
        p {{ margin-bottom: 16px; }}
        
        img {{
            max-width: 100%;
            height: auto;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin: 10px 0;
        }}
        
        code {{
            background-color: #f6f8fa;
            border-radius: 3px;
            font-size: 85%;
            margin: 0;
            padding: 0.2em 0.4em;
        }}
        
        pre {{
            background-color: #f6f8fa;
            border-radius: 6px;
            font-size: 85%;
            line-height: 1.45;
            overflow: auto;
            padding: 16px;
        }}
        
        blockquote {{
            border-left: 4px solid #dfe2e5;
            margin: 0;
            padding: 0 16px;
            color: #6a737d;
        }}
        
        ul, ol {{
            padding-left: 30px;
        }}
        
        li {{
            margin-bottom: 4px;
        }}
        
        /* 表格樣式已在內容中定義 */
        
        /* Checkbox 樣式 */
        input[type="checkbox"] {{
            margin-right: 8px;
        }}
        
        /* 填空線和回答線樣式已在內容中定義 */
        
        .metadata {{
            background-color: #f8f9fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 16px;
            margin: 20px 0;
            font-size: 14px;
            color: #586069;
        }}
    </style>{mathjax_config}
</head>
<body>
    <div class="metadata">
        <strong>📊 文檔信息</strong><br>
        生成時間: {metadata.get('processing_time', 'N/A')}秒 | 
        字數: {metadata.get('word_count', 'N/A')} | 
        字符數: {metadata.get('char_count', 'N/A')} | 
        行數: {metadata.get('lines_count', 'N/A')}
        {f" | 數學公式: {metadata.get('math_formulas_found', 0)} 個" if has_math else ""}
        {f" | 圖片: {metadata.get('images_found', 0)} 張" if metadata.get('images_found', 0) > 0 else ""}
    </div>
    
    {content}
    
    <div class="metadata" style="margin-top: 40px;">
        <strong>🔧 生成工具</strong><br>
        由 Markdown 轉換工具生成 | 解析器: {metadata.get('parser', 'markdown-it-py')} | 版本: Week 1
    </div>
</body>
</html>'''
        
        return html_template


def main():
    """主程式"""
    parser = argparse.ArgumentParser(
        description='Markdown 到 HTML 轉換工具',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog='''
使用範例:
  python convert_markdown.py document.md
  python convert_markdown.py document.md -o output.html
  python convert_markdown.py TestFile/comprehensive_test.md
        '''
    )
    
    parser.add_argument('input', help='輸入的 Markdown 文件路徑')
    parser.add_argument('-o', '--output', help='輸出的 HTML 文件路徑（可選）')
    parser.add_argument('-v', '--verbose', action='store_true', help='顯示詳細信息')
    
    args = parser.parse_args()
    
    if args.verbose:
        print("🚀 Markdown 轉換工具啟動")
        print(f"📄 輸入文件: {args.input}")
        if args.output:
            print(f"📄 輸出文件: {args.output}")
    
    try:
        converter = LocalMarkdownConverter()
        converter.convert_file(args.input, args.output)
        
        if args.verbose:
            print("🎉 轉換完成！")
            
    except Exception as e:
        print(f"❌ 錯誤: {e}")
        sys.exit(1)


if __name__ == '__main__':
    main()

"""
文檔處理器 - 基於 Final_Plan 簡化實施策略
週 1 版本：專注於 Markdown → HTML 轉換

設計原則：
- 簡單優於複雜
- 使用現成工具 (markdown-it-py)
- 避免過度設計
- 每週可驗證
"""

import time
from typing import Dict, Any, List
from .markdown_parser import MarkdownParser
from .config import settings


class DocumentProcessor:
    """
    主文檔處理器 - 週 1 簡化版本
    
    職責：
    - 統一的文檔處理入口
    - 協調各個處理模組
    - 基礎的錯誤處理
    """
    
    def __init__(self):
        """初始化處理器"""
        # 不在初始化時創建 parser，而是在處理時根據 base_path 動態創建
        self.markdown_parser = None

        # 週 1 狀態記錄
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
    
    def process_document(self, markdown_content: str, options: Dict[str, Any], base_path: str = None) -> Dict[str, Any]:
        """
        主處理方法 - 增強版本

        Args:
            markdown_content: 原始 Markdown 文本
            options: 處理選項
                - output_formats: 輸出格式列表 (週 1 只支援 ['html'])
                - theme: 主題名稱 (週 1 只支援 'default')
            base_path: 基礎路徑，用於圖片路徑解析

        Returns:
            處理結果字典
        """
        start_time = time.time()
        self.processed_count += 1
        
        try:
            # 1. 輸入驗證
            if not markdown_content or not markdown_content.strip():
                raise ValueError("Markdown 內容不能為空")
            
            # 2. 解析 Markdown (使用增強的解析器)
            print(f"🔍 [DEBUG] DocumentProcessor: 傳遞 base_path = {base_path}")
            # 動態創建 MarkdownParser，傳入 base_path
            markdown_parser = MarkdownParser(base_path=base_path)
            html_content = markdown_parser.parse(markdown_content)
            
            # 3. 應用基礎樣式 (週 1 簡化版)
            styled_html = self._apply_basic_styles(html_content)
            
            # 4. 生成結果
            processing_time = round(time.time() - start_time, 2)
            self.success_count += 1
            
            result = {
                'success': True,
                'html_content': styled_html,
                'processing_time': processing_time,
                'metadata': {
                    'word_count': len(markdown_content.split()),
                    'char_count': len(markdown_content),
                    'lines_count': len(markdown_content.splitlines()),
                    'week': 1,
                    'features_used': ['markdown_parsing', 'html_generation']
                },
                'errors': [],
                'warnings': []
            }
            
            return result
            
        except Exception as e:
            # 基礎錯誤處理
            processing_time = round(time.time() - start_time, 2)
            self.error_count += 1
            
            return {
                'success': False,
                'html_content': None,
                'processing_time': processing_time,
                'metadata': {
                    'word_count': 0,
                    'week': 1,
                    'error_type': type(e).__name__
                },
                'errors': [f"處理失敗: {str(e)}"],
                'warnings': []
            }
    
    def _apply_basic_styles(self, html_content: str) -> str:
        """
        應用基礎樣式 - 週 1 簡化版本
        
        Args:
            html_content: 原始 HTML 內容
            
        Returns:
            應用樣式後的完整 HTML
        """
        # 週 1 基礎 CSS 樣式
        basic_css = """
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
                line-height: 1.6;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                color: #333;
                background-color: #fff;
            }
            
            h1, h2, h3, h4, h5, h6 {
                color: #2c3e50;
                margin-top: 1.5em;
                margin-bottom: 0.5em;
            }
            
            h1 { font-size: 2em; border-bottom: 2px solid #3498db; padding-bottom: 0.3em; }
            h2 { font-size: 1.5em; border-bottom: 1px solid #bdc3c7; padding-bottom: 0.2em; }
            h3 { font-size: 1.3em; }
            
            p { margin: 1em 0; }
            
            ul, ol {
                margin: 1em 0;
                padding-left: 2em;
            }
            
            li { margin: 0.5em 0; }
            
            blockquote {
                border-left: 4px solid #3498db;
                margin: 1em 0;
                padding: 0.5em 1em;
                background-color: #f8f9fa;
                color: #555;
            }
            
            code {
                background-color: #f1f2f6;
                padding: 0.2em 0.4em;
                border-radius: 3px;
                font-family: 'Courier New', Consolas, monospace;
                font-size: 0.9em;
            }
            
            pre {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                padding: 1em;
                overflow-x: auto;
                margin: 1em 0;
            }
            
            pre code {
                background-color: transparent;
                padding: 0;
            }
            
            table {
                border-collapse: collapse;
                width: 100%;
                margin: 1em 0;
            }
            
            th, td {
                border: 1px solid #ddd;
                padding: 0.5em;
                text-align: left;
            }
            
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            
            strong { color: #2c3e50; }
            em { color: #7f8c8d; }
            
            /* 週 1 特殊標記 */
            .week1-processed {
                border-top: 3px solid #27ae60;
                padding-top: 1em;
                margin-top: 2em;
            }
            
            .week1-footer {
                margin-top: 2em;
                padding-top: 1em;
                border-top: 1px solid #ecf0f1;
                font-size: 0.9em;
                color: #7f8c8d;
                text-align: center;
            }
        </style>
        """
        
        # 組合完整的 HTML 文檔
        full_html = f"""
        <!DOCTYPE html>
        <html lang="zh-TW">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>教育材料 - 週 1 轉換結果</title>
            {basic_css}
        </head>
        <body>
            <div class="week1-processed">
                {html_content}
            </div>
            <div class="week1-footer">
                📚 教育材料轉換系統 - 週 1 基礎版本 | 生成時間: {time.strftime('%Y-%m-%d %H:%M:%S')}
            </div>
        </body>
        </html>
        """
        
        return full_html
    
    def get_stats(self) -> Dict[str, Any]:
        """獲取處理統計信息"""
        return {
            'processed_count': self.processed_count,
            'success_count': self.success_count,
            'error_count': self.error_count,
            'success_rate': round(self.success_count / max(self.processed_count, 1) * 100, 2),
            'week': 1,
            'features': settings.week1_features
        }

"""
簡化版 Markdown 解析器
專注於解決核心問題：表格、數學公式、圖片、checkbox
"""

import markdown_it
import re
import os
import base64
from pathlib import Path
from typing import Optional


class MarkdownParser:
    """簡化版 Markdown 解析器"""
    
    def __init__(self):
        """初始化解析器"""
        # 使用最基本的配置
        self.md = markdown_it.MarkdownIt().enable('table')
        
        # 統計信息
        self.parse_count = 0
        self.math_formulas_found = 0
        self.images_found = 0
        self.base_path = None
    
    def parse(self, markdown_text: str, base_path: str = None) -> str:
        """主解析方法"""
        try:
            self.parse_count += 1
            self.base_path = base_path
            
            # 統計功能
            self.math_formulas_found = len(re.findall(r'\$[^$]+\$', markdown_text))
            self.images_found = len(re.findall(r'!\[.*?\]\(.*?\)', markdown_text))
            
            # 預處理
            processed_content = self._preprocess(markdown_text)
            
            # 解析
            html = self.md.render(processed_content)
            
            # 後處理
            final_html = self._postprocess(html)
            
            return final_html
            
        except Exception as e:
            raise Exception(f"Markdown 解析失敗: {str(e)}")
    
    def _preprocess(self, content: str) -> str:
        """預處理"""
        # 基礎清理
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        
        # 處理圖片路徑
        if self.base_path:
            content = self._process_images(content)
        
        return content
    
    def _process_images(self, content: str) -> str:
        """處理圖片 - 增強版本，包含詳細調試"""
        print(f"🔍 [DEBUG] 開始處理圖片，base_path: {self.base_path}")

        def replace_image(match):
            alt_text = match.group(1)
            image_path = match.group(2).strip()
            title = match.group(3) if len(match.groups()) >= 3 and match.group(3) else ""

            print(f"🔍 [DEBUG] 找到圖片: alt='{alt_text}', path='{image_path}', title='{title}'")

            # 跳過已經是 URL 或 data URI 的圖片
            if image_path.startswith(('http://', 'https://', 'data:')):
                print(f"🔍 [DEBUG] 跳過外部圖片: {image_path}")
                return match.group(0)

            # 檢查 base_path 是否設置
            if not self.base_path:
                print(f"❌ [ERROR] base_path 未設置，無法處理相對路徑")
                return match.group(0)

            # 構建完整路徑
            full_path = os.path.join(self.base_path, image_path)
            print(f"🔍 [DEBUG] 完整路徑: {full_path}")

            # 檢查文件是否存在
            if not os.path.exists(full_path):
                print(f"❌ [ERROR] 圖片文件不存在: {full_path}")

                # 嘗試使用靜態文件服務路徑
                static_path = f"/TestFile/{image_path}"
                print(f"🔄 [FALLBACK] 使用靜態文件路徑: {static_path}")

                if title:
                    return f'![{alt_text}]({static_path} "{title}")'
                else:
                    return f'![{alt_text}]({static_path})'

            try:
                # 讀取圖片文件
                print(f"🔍 [DEBUG] 開始讀取圖片文件...")
                with open(full_path, 'rb') as f:
                    img_data = f.read()

                file_size = len(img_data)
                print(f"🔍 [DEBUG] 圖片文件大小: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")

                # 檢查文件大小限制（10MB）
                if file_size > 10 * 1024 * 1024:
                    print(f"❌ [ERROR] 圖片文件過大 ({file_size/1024/1024:.2f} MB)，使用靜態文件服務")

                    # 使用靜態文件服務路徑
                    static_path = f"/TestFile/{image_path}"
                    print(f"🔄 [FALLBACK] 使用靜態文件路徑: {static_path}")

                    if title:
                        return f'![{alt_text}]({static_path} "{title}")'
                    else:
                        return f'![{alt_text}]({static_path})'

                # 獲取文件擴展名和 MIME 類型
                ext = Path(image_path).suffix.lower()
                if ext == '.jpg':
                    ext = '.jpeg'

                mime_type = f"image/{ext[1:]}" if ext else "image/png"
                print(f"🔍 [DEBUG] MIME 類型: {mime_type}")

                # 轉換為 base64
                print(f"🔍 [DEBUG] 開始 base64 編碼...")
                b64_data = base64.b64encode(img_data).decode()
                b64_size = len(b64_data)
                print(f"🔍 [DEBUG] Base64 編碼完成，大小: {b64_size:,} bytes ({b64_size/1024/1024:.2f} MB)")

                # 構建 data URL
                data_url = f"data:{mime_type};base64,{b64_data}"

                # 構建新的圖片標記
                if title:
                    result = f'![{alt_text}]({data_url} "{title}")'
                else:
                    result = f'![{alt_text}]({data_url})'

                print(f"✅ [SUCCESS] 圖片轉換成功: {image_path}")
                return result

            except Exception as e:
                print(f"❌ [ERROR] 圖片處理失敗 {image_path}: {e}")
                import traceback
                traceback.print_exc()

                # 嘗試使用靜態文件服務路徑
                static_path = f"/TestFile/{image_path}"
                print(f"🔄 [FALLBACK] 使用靜態文件路徑: {static_path}")

                if title:
                    return f'![{alt_text}]({static_path} "{title}")'
                else:
                    return f'![{alt_text}]({static_path})'

        # 更精確的正則表達式，支援帶 title 的語法
        # 匹配: ![alt](path) 或 ![alt](path "title")
        pattern = r'!\[([^\]]*)\]\(([^)\s]+)(?:\s+"([^"]*)")?\)'

        print(f"🔍 [DEBUG] 使用正則表達式: {pattern}")

        # 查找所有匹配
        matches = list(re.finditer(pattern, content))
        print(f"🔍 [DEBUG] 找到 {len(matches)} 個圖片標記")

        # 執行替換
        result = re.sub(pattern, replace_image, content)

        print(f"🔍 [DEBUG] 圖片處理完成")
        return result
    
    def _postprocess(self, html: str) -> str:
        """後處理"""
        # 1. 增強表格樣式
        html = html.replace('<table>', '''<table style="
            border-collapse: collapse; 
            width: 100%; 
            margin: 1em 0; 
            border: 1px solid #ddd;
        ">''')
        
        html = html.replace('<th>', '''<th style="
            background-color: #f8f9fa; 
            border: 1px solid #ddd; 
            padding: 8px 12px; 
            font-weight: bold;
        ">''')
        
        html = html.replace('<td>', '''<td style="
            border: 1px solid #ddd; 
            padding: 8px 12px;
        ">''')
        
        # 2. 處理 checkbox
        html = re.sub(
            r'<li>\s*\[\s*\]\s*',
            '<li><input type="checkbox" disabled style="margin-right: 8px;"> ',
            html
        )
        
        html = re.sub(
            r'<li>\s*\[x\]\s*',
            '<li><input type="checkbox" checked disabled style="margin-right: 8px;"> ',
            html,
            flags=re.IGNORECASE
        )
        
        # 3. 處理回答線
        html = re.sub(
            r'<p>---</p>',
            '<div style="border-bottom: 1px solid #666; height: 20px; margin: 8px 0;"></div>',
            html
        )
        
        # 4. 處理填空線
        html = re.sub(
            r'_{4,}',
            '<span style="border-bottom: 2px solid #333; min-width: 100px; display: inline-block;">&nbsp;&nbsp;&nbsp;&nbsp;</span>',
            html
        )
        
        # 5. 添加數學公式支援
        if self.math_formulas_found > 0:
            mathjax_config = '''
<script>
window.MathJax = {
  tex: {
    inlineMath: [['$', '$']],
    displayMath: [['$$', '$$']],
    packages: {'[+]': ['mhchem']},
    processEscapes: true
  }
};
</script>
<script async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
<script async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/input/tex/extensions/mhchem.js"></script>
'''
            html = mathjax_config + html
        
        return html
    
    def get_stats(self) -> dict:
        """獲取統計信息"""
        return {
            'parse_count': self.parse_count,
            'math_formulas_found': self.math_formulas_found,
            'images_found': self.images_found,
            'week': 1,
            'parser': 'markdown-it-py-simple'
        }
    
    def validate_markdown(self, content: str) -> tuple[bool, Optional[str]]:
        """驗證 Markdown 內容"""
        if not content or not content.strip():
            return False, "內容不能為空"
        
        if len(content) > 1024 * 1024:
            return False, "文件過大，請確保文件小於 1MB"
        
        return True, None

"""
修復版 Markdown 解析器
專注於解決表格、數學公式、圖片和 checkbox 問題
"""

import markdown_it
import re
import os
import base64
from pathlib import Path
from typing import Optional


class MarkdownParser:
    """修復版 Markdown 解析器"""
    
    def __init__(self):
        """初始化解析器"""
        # 使用 markdown-it-py，啟用基本功能
        self.md = markdown_it.MarkdownIt("commonmark", {
            "html": True,
            "linkify": False,  # 暫時禁用 linkify
            "typographer": True,
        }).enable([
            'table',
            'strikethrough'
        ])
        
        # 統計信息
        self.parse_count = 0
        self.math_formulas_found = 0
        self.images_found = 0
        self.base_path = None
    
    def parse(self, markdown_text: str, base_path: str = None) -> str:
        """
        主解析方法
        
        Args:
            markdown_text: 原始 Markdown 文本
            base_path: 基礎路徑，用於圖片路徑解析
            
        Returns:
            解析後的 HTML 內容
        """
        try:
            self.parse_count += 1
            self.base_path = base_path
            
            # 1. 預處理
            processed_content = self._preprocess_content(markdown_text)
            
            # 2. 使用 markdown-it-py 解析
            html = self.md.render(processed_content)
            
            # 3. 後處理
            final_html = self._postprocess_html(html)
            
            return final_html
            
        except Exception as e:
            raise Exception(f"Markdown 解析失敗: {str(e)}")
    
    def _preprocess_content(self, content: str) -> str:
        """預處理內容"""
        # 統計功能元素
        self._count_features(content)
        
        # 基礎清理
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        content = re.sub(r'\n{3,}', '\n\n', content)
        
        # 保護數學公式
        content = self._protect_math_formulas(content)
        
        # 處理圖片路徑
        content = self._process_image_paths(content)
        
        return content
    
    def _count_features(self, content: str) -> None:
        """統計功能元素"""
        # 統計數學公式
        inline_math = len(re.findall(r'\$[^$\n]+\$', content))
        display_math = len(re.findall(r'\$\$[^$]+\$\$', content, re.DOTALL))
        self.math_formulas_found = inline_math + display_math
        
        # 統計圖片
        self.images_found = len(re.findall(r'!\[.*?\]\(.*?\)', content))
    
    def _protect_math_formulas(self, content: str) -> str:
        """保護數學公式"""
        # 保護行中數學公式 $$...$$
        display_math_pattern = r'\$\$(.*?)\$\$'
        content = re.sub(display_math_pattern, 
                        lambda m: f'<div class="math-display" data-math="{self._encode_math(m.group(1))}">{m.group(0)}</div>', 
                        content, flags=re.DOTALL)
        
        # 保護行內數學公式 $...$
        inline_math_pattern = r'(?<!\$)\$([^$\n]+?)\$(?!\$)'
        content = re.sub(inline_math_pattern, 
                        lambda m: f'<span class="math-inline" data-math="{self._encode_math(m.group(1))}">{m.group(0)}</span>', 
                        content)
        
        return content
    
    def _encode_math(self, math_content: str) -> str:
        """編碼數學公式內容"""
        return base64.b64encode(math_content.encode()).decode()
    
    def _process_image_paths(self, content: str) -> str:
        """處理圖片路徑"""
        def replace_image(match):
            alt_text = match.group(1)
            image_path = match.group(2)
            title = match.group(3) if match.group(3) else ""
            
            # 處理相對路徑
            if self.base_path and not image_path.startswith(('http://', 'https://', 'data:')):
                full_path = os.path.join(self.base_path, image_path)
                
                # 嘗試轉換為 base64
                try:
                    if os.path.exists(full_path):
                        base64_data = self._image_to_base64(full_path)
                        if base64_data:
                            return f'![{alt_text}]({base64_data}"{title}")'
                except Exception:
                    pass
            
            # 保持原始路徑
            return match.group(0)
        
        # 匹配圖片語法
        image_pattern = r'!\[([^\]]*)\]\(([^)]+?)(?:\s+"([^"]*)")?\)'
        content = re.sub(image_pattern, replace_image, content)
        
        return content
    
    def _image_to_base64(self, image_path: str) -> str:
        """將圖片轉換為 base64 格式"""
        try:
            with open(image_path, 'rb') as img_file:
                img_data = img_file.read()
                
            # 獲取圖片格式
            img_format = Path(image_path).suffix.lower()
            if img_format == '.jpg':
                img_format = '.jpeg'
            
            # 生成 base64 數據 URL
            base64_data = base64.b64encode(img_data).decode()
            mime_type = f"image/{img_format[1:]}" if img_format else "image/png"
            
            return f"data:{mime_type};base64,{base64_data}"
            
        except Exception:
            return ""
    
    def _postprocess_html(self, html: str) -> str:
        """後處理 HTML"""
        # 1. 處理填空線
        html = re.sub(
            r'_{4,}', 
            '<span class="fill-blank" style="border-bottom: 2px solid #333; min-width: 100px; display: inline-block;">&nbsp;&nbsp;&nbsp;&nbsp;</span>', 
            html
        )
        
        # 2. 處理回答題橫線
        html = re.sub(
            r'<p>---</p>',
            '<div class="answer-line" style="border-bottom: 1px solid #666; height: 20px; margin: 8px 0;"></div>',
            html
        )
        
        # 3. 增強表格樣式
        html = self._enhance_table_styles(html)
        
        # 4. 處理數學公式渲染
        html = self._render_math_formulas(html)
        
        # 5. 處理 checkbox（手動實現）
        html = self._process_checkboxes(html)
        
        # 6. 添加 MathJax 配置
        if self.math_formulas_found > 0:
            html = self._add_mathjax_config() + html
        
        return html
    
    def _enhance_table_styles(self, html: str) -> str:
        """增強表格樣式"""
        # 基本表格樣式
        html = html.replace('<table>', '''<table style="
            border-collapse: collapse; 
            width: 100%; 
            margin: 1em 0; 
            border: 1px solid #ddd;
            font-size: 14px;
        ">''')
        
        # 表頭樣式
        html = html.replace('<th>', '''<th style="
            background-color: #f8f9fa; 
            border: 1px solid #ddd; 
            padding: 8px 12px; 
            text-align: left;
            font-weight: bold;
        ">''')
        
        # 表格單元格樣式
        html = html.replace('<td>', '''<td style="
            border: 1px solid #ddd; 
            padding: 8px 12px; 
            vertical-align: top;
        ">''')
        
        return html
    
    def _render_math_formulas(self, html: str) -> str:
        """渲染數學公式"""
        # 恢復行中數學公式
        def restore_display_math(match):
            encoded_math = match.group(1)
            try:
                math_content = base64.b64decode(encoded_math).decode()
                return f'$$\\displaystyle {math_content}$$'
            except:
                return match.group(0)
        
        html = re.sub(r'<div class="math-display" data-math="([^"]+)">[^<]*</div>', 
                     restore_display_math, html)
        
        # 恢復行內數學公式
        def restore_inline_math(match):
            encoded_math = match.group(1)
            try:
                math_content = base64.b64decode(encoded_math).decode()
                return f'${math_content}$'
            except:
                return match.group(0)
        
        html = re.sub(r'<span class="math-inline" data-math="([^"]+)">[^<]*</span>', 
                     restore_inline_math, html)
        
        return html
    
    def _process_checkboxes(self, html: str) -> str:
        """處理 checkbox（手動實現）"""
        # 處理未選中的 checkbox
        html = re.sub(
            r'<li>\s*\[\s*\]\s*',
            '<li><input type="checkbox" disabled="" style="margin-right: 8px;"> ',
            html
        )
        
        # 處理選中的 checkbox
        html = re.sub(
            r'<li>\s*\[x\]\s*',
            '<li><input type="checkbox" checked="" disabled="" style="margin-right: 8px;"> ',
            html,
            flags=re.IGNORECASE
        )
        
        return html
    
    def _add_mathjax_config(self) -> str:
        """添加 MathJax 配置"""
        return '''
<!-- MathJax 配置 -->
<script>
window.MathJax = {
  tex: {
    inlineMath: [['$', '$']],
    displayMath: [['$$', '$$']],
    packages: {'[+]': ['mhchem']},
    processEscapes: true,
    processEnvironments: true
  },
  options: {
    ignoreHtmlClass: 'tex2jax_ignore',
    processHtmlClass: 'tex2jax_process'
  }
};
</script>
<script async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
<script async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/input/tex/extensions/mhchem.js"></script>

'''
    
    def get_stats(self) -> dict:
        """獲取解析統計信息"""
        return {
            'parse_count': self.parse_count,
            'math_formulas_found': self.math_formulas_found,
            'images_found': self.images_found,
            'week': 1,
            'parser': 'markdown-it-py-fixed'
        }
    
    def validate_markdown(self, content: str) -> tuple[bool, Optional[str]]:
        """驗證 Markdown 內容"""
        if not content or not content.strip():
            return False, "內容不能為空"
        
        if len(content) > 1024 * 1024:  # 1MB 限制
            return False, "文件過大，請確保文件小於 1MB"
        
        has_text = bool(re.search(r'\w+', content))
        if not has_text:
            return False, "文件似乎不包含有效的文本內容"
        
        return True, None

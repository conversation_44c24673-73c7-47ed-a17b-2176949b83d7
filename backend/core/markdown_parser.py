"""
Markdown 解析器 - 基於 Final_Plan 簡化實施策略
週 1 版本：使用 markdown-it-py 直接解析

設計原則：
- 使用現成工具 (markdown-it-py)
- 簡單直接的解析
- 為未來功能預留接口 (數學公式、圖片)
- 基礎的教育元素處理
"""

import markdown_it
import re
import os
import base64
from pathlib import Path
from typing import Optional

# 嘗試導入插件，如果失敗則使用基本功能
try:
    from mdit_py_plugins.tasklists import tasklists_plugin
    TASKLISTS_AVAILABLE = True
except ImportError:
    TASKLISTS_AVAILABLE = False

try:
    from PIL import Image
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False


class MarkdownParser:
    """
    Markdown 解析器 - 週 1 簡化版本
    
    職責：
    - 使用 markdown-it-py 解析 Markdown
    - 基礎的教育元素識別
    - 為未來功能預留處理接口
    """
    
    def __init__(self, base_path=None):
        """初始化解析器

        Args:
            base_path: 基礎路徑，用於圖片路徑解析
        """
        # 使用 markdown-it-py 作為基礎解析器，啟用表格和其他功能
        self.md = markdown_it.MarkdownIt("commonmark", {
            "html": True,
            "linkify": True,
            "typographer": True,
        }).enable([
            'table',
            'strikethrough',
            'linkify',
            'replacements',
            'smartquotes'
        ])

        # 設置基礎路徑
        self.base_path = base_path

        # 啟用插件（如果可用）
        if TASKLISTS_AVAILABLE:
            try:
                self.md.use(tasklists_plugin)  # 支援 checkbox
            except Exception:
                pass

        # 統計信息
        self.parse_count = 0
        self.math_formulas_found = 0
        self.images_found = 0
    
    def parse(self, markdown_text: str) -> str:
        """
        主解析方法 - 增強版本

        Args:
            markdown_text: 原始 Markdown 文本

        Returns:
            解析後的 HTML 內容
        """
        try:
            self.parse_count += 1

            # 1. 預處理 (數學公式保護、圖片路徑處理)
            processed_content = self._preprocess_content(markdown_text)

            # 2. 使用 markdown-it-py 解析
            html = self.md.render(processed_content)

            # 3. 後處理 (教育元素處理、圖片處理、數學公式)
            final_html = self._postprocess_html(html)

            return final_html

        except Exception as e:
            raise Exception(f"Markdown 解析失敗: {str(e)}")
    
    def _preprocess_content(self, content: str) -> str:
        """
        預處理內容 - 增強版本

        功能：
        - 數學公式保護
        - 圖片路徑處理
        - 基礎清理

        Args:
            content: 原始 Markdown 內容

        Returns:
            預處理後的內容
        """
        # 統計功能元素
        self._count_future_features(content)

        # 基礎清理
        content = content.replace('\r\n', '\n').replace('\r', '\n')
        content = re.sub(r'\n{3,}', '\n\n', content)

        # 保護數學公式（避免被 Markdown 解析器破壞）
        content = self._protect_math_formulas(content)

        # 處理圖片路徑
        content = self._process_image_paths(content)

        return content
    
    def _count_future_features(self, content: str) -> None:
        """
        統計未來功能元素 - 週 1 預備統計
        
        Args:
            content: Markdown 內容
        """
        # 統計數學公式 (週 3 會用到)
        inline_math = len(re.findall(r'\$[^$]+\$', content))
        display_math = len(re.findall(r'\$\$[^$]+\$\$', content, re.DOTALL))
        self.math_formulas_found = inline_math + display_math
        
        # 統計圖片
        self.images_found = len(re.findall(r'!\[.*?\]\(.*?\)', content))

    def _protect_math_formulas(self, content: str) -> str:
        """
        保護數學公式，避免被 Markdown 解析器破壞 - 簡化版本

        Args:
            content: 原始內容

        Returns:
            保護後的內容
        """
        # 簡化處理：不進行保護，直接讓 markdown-it-py 處理
        # 數學公式會在後處理階段由 MathJax 渲染
        return content

    def _encode_math(self, math_content: str) -> str:
        """編碼數學公式內容"""
        return base64.b64encode(math_content.encode()).decode()

    def _process_image_paths(self, content: str) -> str:
        """
        處理圖片路徑，使用相對路徑而不是 base64

        Args:
            content: 原始內容

        Returns:
            處理後的內容
        """
        # 不進行 base64 轉換，保持原始路徑
        # 圖片路徑將在後處理階段修復
        return content

    def _image_to_base64(self, image_path: str) -> str:
        """
        將圖片轉換為 base64 格式

        Args:
            image_path: 圖片路徑

        Returns:
            base64 格式的圖片數據，如果失敗返回空字符串
        """
        try:
            with open(image_path, 'rb') as img_file:
                img_data = img_file.read()

            # 獲取圖片格式
            img_format = Path(image_path).suffix.lower()
            if img_format == '.jpg':
                img_format = '.jpeg'

            # 生成 base64 數據 URL
            base64_data = base64.b64encode(img_data).decode()
            mime_type = f"image/{img_format[1:]}" if img_format else "image/png"

            return f"data:{mime_type};base64,{base64_data}"

        except Exception as e:
            # 靜默處理錯誤，返回原路徑
            return ""
    
    def _postprocess_html(self, html: str) -> str:
        """
        後處理 HTML - 增強版本

        Args:
            html: markdown-it-py 生成的 HTML

        Returns:
            後處理後的 HTML
        """
        # 1. 處理填空線 (教育特殊功能)
        html = re.sub(
            r'_{4,}',
            '<span class="fill-blank" style="border-bottom: 2px solid #333; min-width: 100px; display: inline-block;">&nbsp;&nbsp;&nbsp;&nbsp;</span>',
            html
        )

        # 2. 處理回答題橫線 (3-4條橫線)
        html = re.sub(
            r'<p>---</p>',
            '<div class="answer-line" style="border-bottom: 1px solid #666; height: 20px; margin: 8px 0;"></div>',
            html
        )

        # 3. 增強表格樣式
        html = self._enhance_table_styles(html)

        # 4. 處理數學公式渲染
        html = self._render_math_formulas(html)

        # 5. 增強列表樣式
        html = html.replace('<ul>', '<ul class="edu-list" style="margin: 0.5em 0; padding-left: 1.5em;">')
        html = html.replace('<ol>', '<ol class="edu-list" style="margin: 0.5em 0; padding-left: 1.5em;">')

        # 6. 處理 checkbox
        html = self._enhance_checkboxes(html)

        # 7. 修復圖片路徑 - 根據使用場景選擇路徑格式
        html = self._fix_image_paths(html)

        print(f"🔍 [DEBUG] 圖片路徑修復後的 HTML 片段: {html[:200]}...")

        # 8. 添加 MathJax 配置（如果有數學公式）
        if self.math_formulas_found > 0:
            html = self._add_mathjax_config() + html

        return html

    def _fix_image_paths(self, html: str) -> str:
        """
        修復圖片路徑 - 使用相對路徑

        將 photo/ 路徑轉換為相對於 HTML 文件的路徑
        """
        import os

        if self.base_path:
            # 本地文件系統模式：保持相對路徑
            # 不需要修改，因為 HTML 文件和圖片在同一項目中
            print(f"🔍 [DEBUG] 使用相對路徑，base_path: {self.base_path}")
            print(f"🔍 [DEBUG] 保持原始圖片路徑格式")
        else:
            # Web 服務器模式：使用靜態文件路徑（向後兼容）
            html = re.sub(r'src="photo/([^"]+)"', r'src="/TestFile/photo/\1"', html)
            html = re.sub(r"src='photo/([^']+)'", r"src='/TestFile/photo/\1'", html)

            print(f"🔍 [DEBUG] 使用 Web 服務器路徑")

        return html

    def _enhance_table_styles(self, html: str) -> str:
        """增強表格樣式"""
        # 基本表格樣式
        html = html.replace('<table>', '''<table class="edu-table" style="
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
            border: 1px solid #ddd;
            font-size: 14px;
        ">''')

        # 表頭樣式
        html = html.replace('<th>', '''<th style="
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
            font-weight: bold;
        ">''')

        # 表格單元格樣式
        html = html.replace('<td>', '''<td style="
            border: 1px solid #ddd;
            padding: 8px 12px;
            vertical-align: top;
        ">''')

        return html

    def _render_math_formulas(self, html: str) -> str:
        """渲染數學公式 - 簡化版本"""
        # 不需要恢復，因為我們不再進行保護
        # 數學公式會直接由 MathJax 處理
        return html

    def _enhance_checkboxes(self, html: str) -> str:
        """增強 checkbox 樣式"""
        # 未選中的 checkbox
        html = html.replace(
            '<input type="checkbox" disabled="">',
            '<input type="checkbox" disabled="" style="margin-right: 8px; transform: scale(1.2);">'
        )

        # 選中的 checkbox
        html = html.replace(
            '<input type="checkbox" checked="" disabled="">',
            '<input type="checkbox" checked="" disabled="" style="margin-right: 8px; transform: scale(1.2);">'
        )

        return html

    def _add_mathjax_config(self) -> str:
        """添加 MathJax 配置 - 離線友好版本"""
        return '''
<!-- MathJax 3.0 配置 - 離線友好版本 -->
<script>
window.MathJax = {
  tex: {
    inlineMath: [['$', '$']],
    displayMath: [['$$', '$$']],
    processEscapes: true,
    processEnvironments: true
  },
  startup: {
    ready: () => {
      console.log('🔢 MathJax 3.0 正在初始化...');
      MathJax.startup.defaultReady();
    },
    pageReady: () => {
      console.log('🔢 MathJax 頁面準備完成，開始渲染數學公式...');
      return MathJax.startup.defaultPageReady().then(() => {
        console.log('🔢 MathJax 渲染完成！');
      });
    }
  }
};
</script>
<!-- 使用更可靠的 CDN -->
<script id="MathJax-script" src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"
        onerror="console.log('⚠️ MathJax CDN 載入失敗，數學公式可能無法顯示')"></script>

'''

    def get_stats(self) -> dict:
        """獲取解析統計信息"""
        return {
            'parse_count': self.parse_count,
            'math_formulas_found': self.math_formulas_found,
            'images_found': self.images_found,
            'week': 1,
            'parser': 'markdown-it-py'
        }
    
    def validate_markdown(self, content: str) -> tuple[bool, Optional[str]]:
        """
        驗證 Markdown 內容 - 週 1 基礎驗證
        
        Args:
            content: Markdown 內容
            
        Returns:
            (是否有效, 錯誤信息)
        """
        if not content or not content.strip():
            return False, "內容不能為空"
        
        # 基礎長度檢查
        if len(content) > 1024 * 1024:  # 1MB 限制
            return False, "文件過大，請確保文件小於 1MB"
        
        # 檢查是否包含基本的 Markdown 元素
        has_headers = bool(re.search(r'^#+\s', content, re.MULTILINE))
        has_text = bool(re.search(r'\w+', content))
        
        if not has_text:
            return False, "文件似乎不包含有效的文本內容"
        
        return True, None

"""
最簡化的 Markdown 解析器
專注於快速修復圖片顯示問題
"""

import markdown_it
import re
import os
import base64
from pathlib import Path
from typing import Optional


class MarkdownParser:
    """最簡化的 Markdown 解析器"""
    
    def __init__(self):
        """初始化解析器"""
        self.md = markdown_it.MarkdownIt().enable('table')
        self.parse_count = 0
        self.math_formulas_found = 0
        self.images_found = 0
        self.base_path = None
    
    def parse(self, markdown_text: str, base_path: str = None) -> str:
        """主解析方法"""
        try:
            self.parse_count += 1
            self.base_path = base_path
            
            print(f"🔍 [DEBUG] 開始解析，base_path: {base_path}")
            
            # 統計
            self.math_formulas_found = len(re.findall(r'\$[^$]+\$', markdown_text))
            self.images_found = len(re.findall(r'!\[.*?\]\(.*?\)', markdown_text))
            
            # 處理圖片
            processed_content = self._process_images(markdown_text)
            
            # 解析
            html = self.md.render(processed_content)
            
            # 後處理
            final_html = self._postprocess(html)
            
            return final_html
            
        except Exception as e:
            print(f"❌ [ERROR] 解析失敗: {e}")
            import traceback
            traceback.print_exc()
            raise Exception(f"Markdown 解析失敗: {str(e)}")
    
    def _process_images(self, content: str) -> str:
        """處理圖片 - 簡化版本"""
        if not self.base_path:
            print("⚠️ [WARNING] base_path 未設置，跳過圖片處理")
            return content
        
        def replace_image(match):
            alt_text = match.group(1)
            image_path = match.group(2)
            
            print(f"🔍 [DEBUG] 處理圖片: {image_path}")
            
            # 跳過外部圖片
            if image_path.startswith(('http://', 'https://', 'data:')):
                return match.group(0)
            
            # 直接使用靜態文件服務路徑
            static_path = f"/TestFile/{image_path}"
            print(f"🔄 [INFO] 使用靜態文件路徑: {static_path}")
            
            return f'![{alt_text}]({static_path})'
        
        # 簡單的圖片匹配
        pattern = r'!\[([^\]]*)\]\(([^)]+)\)'
        result = re.sub(pattern, replace_image, content)
        
        return result
    
    def _postprocess(self, html: str) -> str:
        """後處理 - 簡化版本"""
        # 表格樣式
        html = html.replace('<table>', '<table style="border-collapse: collapse; width: 100%; margin: 1em 0; border: 1px solid #ddd;">')
        html = html.replace('<th>', '<th style="background-color: #f8f9fa; border: 1px solid #ddd; padding: 8px 12px; font-weight: bold;">')
        html = html.replace('<td>', '<td style="border: 1px solid #ddd; padding: 8px 12px;">')
        
        # Checkbox
        html = re.sub(r'<li>\s*\[\s*\]\s*', '<li><input type="checkbox" disabled style="margin-right: 8px;"> ', html)
        html = re.sub(r'<li>\s*\[x\]\s*', '<li><input type="checkbox" checked disabled style="margin-right: 8px;"> ', html, flags=re.IGNORECASE)
        
        # 回答線
        html = re.sub(r'<p>---</p>', '<div style="border-bottom: 1px solid #666; height: 20px; margin: 8px 0;"></div>', html)
        
        # 數學公式
        if self.math_formulas_found > 0:
            mathjax_config = '''
<script>
window.MathJax = {
  tex: {
    inlineMath: [['$', '$']],
    displayMath: [['$$', '$$']],
    processEscapes: true
  }
};
</script>
<script async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
'''
            html = mathjax_config + html
        
        return html
    
    def get_stats(self) -> dict:
        """獲取統計信息"""
        return {
            'parse_count': self.parse_count,
            'math_formulas_found': self.math_formulas_found,
            'images_found': self.images_found,
            'week': 1,
            'parser': 'markdown-it-py-minimal'
        }
    
    def validate_markdown(self, content: str) -> tuple[bool, Optional[str]]:
        """驗證 Markdown 內容"""
        if not content or not content.strip():
            return False, "內容不能為空"
        
        if len(content) > 1024 * 1024:
            return False, "文件過大，請確保文件小於 1MB"
        
        return True, None

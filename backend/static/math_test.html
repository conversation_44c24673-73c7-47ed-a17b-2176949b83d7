<!DOCTYPE html>
<html>
<head>
    <title>數學公式測試頁面</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 8px; }
        .formula { margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 4px; }
    </style>
</head>
<body>
    <h1>🔢 數學公式測試頁面</h1>
    
    <div class="test-section">
        <h2>行內數學公式測試</h2>
        <div class="formula">
            <p>愛因斯坦質能方程：$E = mc^2$</p>
            <p>圓的面積公式：$A = \pi r^2$</p>
            <p>二次方程式：$ax^2 + bx + c = 0$</p>
            <p>勾股定理：$a^2 + b^2 = c^2$</p>
        </div>
    </div>
    
    <div class="test-section">
        <h2>行中數學公式測試</h2>
        <div class="formula">
            <p>高斯積分：</p>
            $$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$
            
            <p>自然數求和：</p>
            $$\sum_{i=1}^{n} i = \frac{n(n+1)}{2}$$
            
            <p>極限定義：</p>
            $$\lim_{x \to 0} \frac{\sin x}{x} = 1$$
        </div>
    </div>
    
    <div class="test-section">
        <h2>複雜數學公式測試</h2>
        <div class="formula">
            <p>波動方程：</p>
            $$\frac{\partial^2 u}{\partial t^2} = c^2 \nabla^2 u$$
            
            <p>矩陣運算：</p>
            $$\begin{pmatrix}
            a & b \\
            c & d
            \end{pmatrix}
            \begin{pmatrix}
            x \\
            y
            \end{pmatrix}
            =
            \begin{pmatrix}
            ax + by \\
            cx + dy
            \end{pmatrix}$$
            
            <p>分數和根號：</p>
            $$x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$$
        </div>
    </div>
    
    <div class="test-section">
        <h2>狀態檢查</h2>
        <div id="status">
            <p>⏳ 正在載入 MathJax...</p>
        </div>
    </div>

    <!-- MathJax 3.0 配置 -->
    <script>
    window.MathJax = {
      tex: {
        inlineMath: [['$', '$'], ['\\(', '\\)']],
        displayMath: [['$$', '$$'], ['\\[', '\\]']],
        processEscapes: true,
        processEnvironments: true,
        packages: {'[+]': ['base', 'ams', 'noerrors', 'noundefined']}
      },
      options: {
        ignoreHtmlClass: 'tex2jax_ignore',
        processHtmlClass: 'tex2jax_process'
      },
      startup: {
        ready: () => {
          console.log('🔢 MathJax 3.0 正在初始化...');
          document.getElementById('status').innerHTML = '<p>🔢 MathJax 正在初始化...</p>';
          MathJax.startup.defaultReady();
        },
        pageReady: () => {
          console.log('🔢 MathJax 頁面準備完成，開始渲染數學公式...');
          document.getElementById('status').innerHTML = '<p>🔢 正在渲染數學公式...</p>';
          return MathJax.startup.defaultPageReady().then(() => {
            console.log('🔢 MathJax 渲染完成！');
            document.getElementById('status').innerHTML = '<p>✅ MathJax 渲染完成！所有數學公式應該正常顯示。</p>';
          });
        }
      }
    };
    </script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
</body>
</html>

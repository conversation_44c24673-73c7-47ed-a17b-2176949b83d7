<!DOCTYPE html>
<html>
<head>
    <title>簡單功能測試</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .test-area { 
            border: 2px dashed #ccc; 
            padding: 40px; 
            text-align: center; 
            margin: 20px 0; 
            border-radius: 8px;
            background: #fafafa;
        }
        .test-area.dragover { 
            border-color: #007bff; 
            background: #e3f2fd; 
        }
        button { 
            background: #007bff; 
            color: white; 
            padding: 12px 24px; 
            border: none; 
            border-radius: 6px; 
            cursor: pointer; 
            margin: 10px;
        }
        button:hover { background: #0056b3; }
        .result { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; }
    </style>
</head>
<body>
    <h1>🧪 簡單功能測試</h1>
    
    <div class="test-area" id="testArea">
        <h3>拖拽測試區域</h3>
        <p>拖拽文件或文件夾到這裡</p>
        <div>
            <button onclick="testFolderSelect()">📁 測試文件夾選擇</button>
            <button onclick="testFileSelect()">📄 測試文件選擇</button>
        </div>
    </div>
    
    <input type="file" id="folderInput" webkitdirectory multiple style="display: none;" />
    <input type="file" id="fileInput" accept=".md,.txt,.markdown" style="display: none;" />
    
    <div id="result" class="result">
        <h3>📊 測試結果</h3>
        <div id="log">等待測試...</div>
    </div>

    <script>
        let testLog = [];
        
        function log(message) {
            testLog.push(`${new Date().toLocaleTimeString()}: ${message}`);
            document.getElementById('log').innerHTML = testLog.join('<br>');
            console.log(message);
        }
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('✅ DOM 載入完成');
            
            const testArea = document.getElementById('testArea');
            const folderInput = document.getElementById('folderInput');
            const fileInput = document.getElementById('fileInput');
            
            if (testArea) {
                log('✅ 找到測試區域元素');
                
                // 拖拽事件
                testArea.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    e.currentTarget.classList.add('dragover');
                    log('🔄 拖拽懸停');
                });
                
                testArea.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    e.currentTarget.classList.remove('dragover');
                    log('🔄 拖拽離開');
                });
                
                testArea.addEventListener('drop', function(e) {
                    e.preventDefault();
                    e.currentTarget.classList.remove('dragover');
                    log('🎯 檔案被拖拽放下');
                    
                    const files = e.dataTransfer.files;
                    log(`📁 拖拽文件數量: ${files.length}`);
                    
                    for (let i = 0; i < files.length; i++) {
                        log(`📄 文件 ${i+1}: ${files[i].name} (${files[i].size} bytes)`);
                    }
                });
                
                log('✅ 拖拽事件綁定完成');
            } else {
                log('❌ 找不到測試區域元素');
            }
            
            // 文件選擇事件
            if (folderInput) {
                folderInput.addEventListener('change', function(e) {
                    log(`📁 文件夾選擇: ${e.target.files.length} 個文件`);
                    for (let i = 0; i < e.target.files.length; i++) {
                        log(`📄 文件 ${i+1}: ${e.target.files[i].name}`);
                    }
                });
                log('✅ 文件夾輸入事件綁定完成');
            } else {
                log('❌ 找不到文件夾輸入元素');
            }
            
            if (fileInput) {
                fileInput.addEventListener('change', function(e) {
                    log(`📄 文件選擇: ${e.target.files.length} 個文件`);
                    for (let i = 0; i < e.target.files.length; i++) {
                        log(`📄 文件 ${i+1}: ${e.target.files[i].name}`);
                    }
                });
                log('✅ 文件輸入事件綁定完成');
            } else {
                log('❌ 找不到文件輸入元素');
            }
        });
        
        function testFolderSelect() {
            log('🔘 文件夾按鈕被點擊');
            const folderInput = document.getElementById('folderInput');
            if (folderInput) {
                log('✅ 找到文件夾輸入元素，觸發點擊');
                folderInput.click();
            } else {
                log('❌ 找不到文件夾輸入元素');
            }
        }
        
        function testFileSelect() {
            log('🔘 文件按鈕被點擊');
            const fileInput = document.getElementById('fileInput');
            if (fileInput) {
                log('✅ 找到文件輸入元素，觸發點擊');
                fileInput.click();
            } else {
                log('❌ 找不到文件輸入元素');
            }
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html>
<head>
    <title>測試頁面</title>
    <meta charset="UTF-8">
</head>
<body>
    <h1>圖片測試</h1>
    <p>直接圖片連結測試：</p>
    <img src="/TestFile/photo/Gemini_Generated_Image_1.png" alt="測試圖片" style="max-width: 200px;">
    
    <h1>數學公式測試</h1>
    <p>行內公式：$E = mc^2$</p>
    <p>行中公式：</p>
    $$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$
    
    <!-- MathJax 配置 -->
    <script>
    window.MathJax = {
      tex: {
        inlineMath: [['$', '$']],
        displayMath: [['$$', '$$']],
        processEscapes: true,
        processEnvironments: true
      },
      startup: {
        ready: () => {
          console.log('MathJax 正在初始化...');
          MathJax.startup.defaultReady();
        },
        pageReady: () => {
          console.log('MathJax 頁面準備完成');
          return MathJax.startup.defaultPageReady();
        }
      }
    };
    </script>
    <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
    <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>
</body>
</html>

# 教育材料轉換系統 - 週 1 綜合測試文件

這是一個全面的 Markdown 測試文件，用於驗證教育材料轉換系統的各項功能。

## 1. 基礎格式測試

### 1.1 標題層級測試

# 一級標題 - 課程主題
## 二級標題 - 章節標題  
### 三級標題 - 小節標題
#### 四級標題 - 重點內容
##### 五級標題 - 細節說明
###### 六級標題 - 補充資料

### 1.2 段落和換行測試

這是第一個段落，包含普通的文字內容。段落之間應該有適當的間距。

這是第二個段落。  
這一行使用了強制換行（兩個空格）。
這一行是正常的換行。

這是第三個段落，測試空行處理。

## 2. 文字樣式測試

### 2.1 基本文字格式

**這是粗體文字（雙星號）**  
__這是粗體文字（雙下劃線）__

*這是斜體文字（單星號）*  
_這是斜體文字（單下劃線）_

***這是粗斜體文字（三星號）***
___這是粗斜體文字（三下劃線）___

`這是行內程式碼`

### 2.2 中英文混排測試

這段文字包含 **English words** 和 *中文字符*，測試 `mixed content` 的顯示效果。

重要的 **教學概念** 需要特別 *強調*，例如：`變數 variable` 和 `函數 function`。

## 3. 列表測試

### 3.1 無序列表（使用不同符號）

使用減號：
- 第一項
- 第二項
- 第三項

使用星號：
* 項目 A
* 項目 B  
* 項目 C

使用加號：
+ 選項 1
+ 選項 2
+ 選項 3

### 3.2 有序列表

1. 第一步：準備材料
2. 第二步：開始實驗
3. 第三步：記錄結果
4. 第四步：分析數據

### 3.3 嵌套列表（多層級）

1. **第一層級：主要學習目標**
   1. **第二層級：理解基本概念**
      1. **第三層級：理論基礎**
         1. **第四層級：核心概念**
            1. **第五層級：基本定義**
            2. **第五層級：重要性質**
         2. **第四層級：應用範圍**
            1. **第五層級：實際應用**
            2. **第五層級：理論延伸**
      2. **第三層級：實踐應用**
         1. **第四層級：操作技能**
            1. **第五層級：基礎操作**
            2. **第五層級：進階技巧**
         2. **第四層級：分析技能**
            1. **第五層級：數據分析**
            2. **第五層級：結果解釋**
   2. **第二層級：掌握實用技能**
      1. **第三層級：技術能力**
         1. **第四層級：程式設計**
            1. **第五層級：語法掌握**
            2. **第五層級：邏輯思維**
         2. **第四層級：問題解決**
            1. **第五層級：分析問題**
            2. **第五層級：設計方案**

2. **第一層級：評估方法**
   1. **第二層級：平時表現 (30%)**
      1. **第三層級：課堂參與 (15%)**
         1. **第四層級：發言質量**
            1. **第五層級：思考深度**
            2. **第五層級：表達清晰度**
         2. **第四層級：互動頻率**
            1. **第五層級：主動提問**
            2. **第五層級：回答問題**
      2. **第三層級：作業完成 (15%)**
         1. **第四層級：完成度**
            1. **第五層級：按時提交**
            2. **第五層級：內容完整**
         2. **第四層級：質量評估**
            1. **第五層級：正確性**
            2. **第五層級：創新性**
   2. **第二層級：期中考試 (30%)**
   3. **第二層級：期末考試 (40%)**

### 3.4 列表項目內的複雜內容

1. **第一章：導論**
   
   這一章介紹課程的基本概念。
   
   - 學習重點：
     - 基礎理論
     - 實際應用
   
   ```
   重要提醒：請仔細閱讀每個章節
   ```

2. **第二章：實踐**
   
   包含實際操作和練習。

## 4. 圖片測試

### 4.1 多張圖片顯示測試

**第一組圖片：**
![圖片1](photo/Gemini_Generated_Image_1.png)
![圖片2](photo/Gemini_Generated_Image_2.png)
![圖片3](photo/Gemini_Generated_Image_3.png)

**第二組圖片（重複測試）：**
![教學圖片A](photo/Gemini_Generated_Image_1.png)
![教學圖片B](photo/Gemini_Generated_Image_2.png)
![教學圖片C](photo/Gemini_Generated_Image_3.png)

**第三組圖片（帶說明）：**
![示意圖1](photo/Gemini_Generated_Image_1.png "第一張示意圖")
![示意圖2](photo/Gemini_Generated_Image_2.png "第二張示意圖")
![示意圖3](photo/Gemini_Generated_Image_3.png "第三張示意圖")

### 4.2 圖片與文字混排測試

這是一段文字，後面跟著圖片：![內嵌圖片](photo/Gemini_Generated_Image_1.png)

**圖片說明測試：**

![教學流程圖](photo/Gemini_Generated_Image_1.png "教學流程示意圖")
*圖 1：教學流程示意圖*

![學習架構圖](photo/Gemini_Generated_Image_2.png "學習架構示意圖")
*圖 2：學習架構示意圖*

![評估方法圖](photo/Gemini_Generated_Image_3.png "評估方法示意圖")
*圖 3：評估方法示意圖*

### 4.3 圖片數量壓力測試

連續多張圖片：
![測試1](photo/Gemini_Generated_Image_1.png)
![測試2](photo/Gemini_Generated_Image_2.png)
![測試3](photo/Gemini_Generated_Image_3.png)
![測試4](photo/Gemini_Generated_Image_1.png)
![測試5](photo/Gemini_Generated_Image_2.png)
![測試6](photo/Gemini_Generated_Image_3.png)
![測試7](photo/Gemini_Generated_Image_1.png)
![測試8](photo/Gemini_Generated_Image_2.png)
![測試9](photo/Gemini_Generated_Image_3.png)

## 5. 表格測試

### 5.1 基本表格

| 學生姓名 | 數學 | 英文 | 中文 |
|---------|------|------|------|
| 張小明   | 85   | 78   | 92   |
| 李小華   | 92   | 85   | 88   |
| 王小美   | 78   | 92   | 85   |

### 5.2 對齊設定表格

| 左對齊 | 居中對齊 | 右對齊 |
|:-------|:-------:|-------:|
| 內容1  | 內容2   | 內容3  |
| 較長的內容 | 中等內容 | 短內容 |

### 5.3 表格內格式化文字

| 科目 | 重要程度 | 學習建議 |
|------|----------|----------|
| **數學** | ⭐⭐⭐⭐⭐ | *每日練習* |
| **英文** | ⭐⭐⭐⭐ | `多聽多說` |
| **科學** | ⭐⭐⭐ | **理解概念** |

### 5.4 課程時間表

| 時間 | 星期一 | 星期二 | 星期三 | 星期四 | 星期五 |
|------|--------|--------|--------|--------|--------|
| 08:30-09:15 | 數學 | 英文 | 數學 | 英文 | 數學 |
| 09:15-10:00 | 中文 | 數學 | 英文 | 數學 | 英文 |
| 10:15-11:00 | 英文 | 科學 | 中文 | 科學 | 中文 |
| 11:00-11:45 | 科學 | 中文 | 科學 | 中文 | 科學 |

## 6. 程式碼測試

### 6.1 行內程式碼

在 Python 中，我們使用 `print()` 函數來輸出內容，例如 `print("Hello World")`。

變數賦值使用 `=` 符號，如 `x = 10` 或 `name = "張三"`。

### 6.2 程式碼區塊（縮排式）

    def hello_world():
        print("Hello, World!")
        return "完成"

    # 這是縮排式的程式碼區塊
    result = hello_world()

### 6.3 圍欄式程式碼區塊

```
這是一個簡單的程式碼區塊
沒有指定語言
可以包含任何文字內容
```

### 6.4 指定語言的程式碼區塊

Python 範例：
```python
def calculate_average(numbers):
    """計算平均值"""
    if not numbers:
        return 0
    return sum(numbers) / len(numbers)

# 測試數據
scores = [85, 92, 78, 96, 88]
avg = calculate_average(scores)
print(f"平均分數：{avg:.2f}")
```

JavaScript 範例：
```javascript
function calculateGrade(score) {
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
}

// 使用範例
const studentScore = 85;
console.log(`成績等級：${calculateGrade(studentScore)}`);
```

HTML 範例：
```html
<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <title>教學網頁</title>
</head>
<body>
    <h1>歡迎來到線上學習平台</h1>
    <p>這是一個<strong>教育</strong>網站。</p>
</body>
</html>
```

## 7. 引用測試

### 7.1 單層引用

> 這是一個重要的教學提醒。學生在學習過程中應該注意理論與實踐的結合。

> **老師提醒**：請務必完成課後練習，這對理解概念非常重要。

### 7.2 多層嵌套引用

> 教育專家指出：
>
> > 有效的學習需要主動參與和持續練習。
> >
> > > 正如孔子所說：「學而時習之，不亦說乎？」

> 現代教育理論強調：
>
> > 學習是一個**主動建構**的過程，包括：
> >
> > 1. 接收新信息
> > 2. 與已有知識連結
> > 3. 實際應用驗證
> >
> > > 因此，*實踐* 是學習的關鍵環節。

### 7.3 引用內的其他格式

> **重要概念**：變數 (Variable)
>
> 在程式設計中，變數是用來儲存數據的容器。例如：
>
> ```python
> name = "學生姓名"
> age = 16
> ```
>
> *注意*：變數名稱應該有意義且易於理解。

## 8. 線條測試

### 8.1 填空線

請完成以下填空題：

1. Python 是一種 ________ 程式語言。
2. 變數 `x = 10` 中，x 的值是 ________。
3. 函數 `print()` 的作用是 ________________。

### 8.2 水平分隔線

使用三個減號：

---

使用三個星號：

***

使用三個下劃線：

___

## 9. 教育專用元素

### 9.1 填空線變化

短填空：答案是 ____

中等填空：學生的姓名是 __________

長填空：請寫出完整的解題過程 ________________________

### 9.2 數學符號和特殊字符

- 希臘字母：α β γ δ ε θ λ μ π σ φ ψ ω
- 數學符號：± × ÷ ≤ ≥ ≠ ≈ ∞ ∑ ∏ √ ∫
- 上下標：H₂O、E=mc²、x^n、log₂
- 分數：½ ⅓ ¼ ¾ ⅛
- 箭頭：→ ← ↑ ↓ ↔ ⇒ ⇔

### 9.3 教學重點標記

⭐ **重點**：這是需要特別注意的內容

⚠️ **注意**：常見錯誤提醒

💡 **提示**：解題技巧分享

✅ **正確**：標準答案或做法

❌ **錯誤**：需要避免的做法

🔍 **深入**：進階內容探討

### 9.4 練習題格式

**練習 1：選擇題**

下列哪個是正確的 Python 語法？

A) `print "Hello"`
B) `print("Hello")`  ✅
C) `print{"Hello"}`
D) `print[Hello]`

**練習 2：填空題**

在 Python 中，用來定義函數的關鍵字是 ________。

**練習 3：簡答題**

請解釋變數和常數的區別。

---
---
---

**練習 4：回答題**

請說明程式設計中「迴圈」的概念及其重要性。

---
---
---
---

**練習 5：論述題**

分析比較 Python 和 JavaScript 兩種程式語言的特點，並說明它們各自適用的場景。

---
---
---
---
---

**練習 6：計算題**

給定一個數列：2, 4, 6, 8, 10...
1) 寫出這個數列的通項公式
2) 計算前10項的和

解答：
1) 通項公式：

---
---

2) 前10項和的計算過程：

---
---
---
---

**練習 7：程式設計題**

編寫一個 Python 函數，計算給定數字的階乘。

程式碼：
```python
# 請在下方寫出完整的函數定義

---
---
---
---

# 測試程式碼

---
---
```

**練習 8：分析題**

閱讀以下程式碼片段，分析其功能並指出可能的問題：

```python
def mystery_function(lst):
    result = []
    for i in range(len(lst)):
        if lst[i] % 2 == 0:
            result.append(lst[i] * 2)
    return result
```

分析：
1) 程式功能說明：

---
---
---

2) 可能存在的問題：

---
---
---

3) 改進建議：

---
---
---

## 10. 中文內容測試

### 10.1 繁體中文字符

**傳統文化**：中華文化博大精深，包含豐富的哲學思想、文學藝術和科學技術。

**現代教育**：香港的教育制度融合了中西方的教學理念，注重培養學生的創新思維和實踐能力。

### 10.2 中英文混排

在 **Computer Science（電腦科學）** 領域中，*Algorithm（演算法）* 是解決問題的重要工具。

學習 `Programming（程式設計）` 需要掌握：
- **Syntax（語法）** - 程式語言的規則
- **Logic（邏輯）** - 解決問題的思路
- **Practice（實踐）** - 動手編寫程式

### 10.3 中文標點符號

「學而時習之，不亦說乎？」——《論語》

現代教育強調：學習、思考、實踐；這三者缺一不可。

重要提醒：請注意以下幾點——
1. 認真聽講；
2. 積極參與；
3. 按時完成作業。

### 10.4 特殊中文教育術語

- **學科專有名詞**：數學、物理、化學、生物、地理、歷史
- **教學方法**：啟發式教學、合作學習、探究式學習、翻轉課堂
- **評估術語**：形成性評估、總結性評估、同儕評估、自我評估
- **香港教育**：中學文憑考試（DSE）、校本評核（SBA）、其他學習經歷（OLE）

## 11. 複雜組合測試

### 11.1 表格內的列表

| 學習階段 | 主要任務 | 具體要求 |
|----------|----------|----------|
| **準備階段** | 基礎知識學習 | • 閱讀教材<br>• 理解概念<br>• 記錄重點 |
| **實踐階段** | 動手操作 | 1. 完成練習<br>2. 解決問題<br>3. 總結經驗 |
| **評估階段** | 檢驗成果 | - 自我檢測<br>- 同儕互評<br>- 教師評估 |

### 11.2 引用內的程式碼

> **程式設計最佳實踐**
>
> 在編寫程式時，應該遵循以下原則：
>
> ```python
> # 1. 使用有意義的變數名稱
> student_name = "張小明"  # 好的命名
> x = "張小明"            # 不好的命名
>
> # 2. 添加適當的註釋
> def calculate_average(scores):
>     """計算學生成績的平均值"""
>     return sum(scores) / len(scores)
> ```
>
> > 記住：**好的程式碼是給人看的，順便讓電腦執行。**

### 11.3 列表內的表格

1. **第一學期成績統計**

   | 科目 | 平均分 | 最高分 | 最低分 |
   |------|--------|--------|--------|
   | 數學 | 85.2   | 98     | 72     |
   | 英文 | 78.5   | 92     | 65     |

2. **第二學期成績統計**

   | 科目 | 平均分 | 最高分 | 最低分 |
   |------|--------|--------|--------|
   | 數學 | 87.1   | 99     | 75     |
   | 英文 | 81.3   | 95     | 68     |

### 11.4 多種格式混合使用

> **重要通知**：期末考試安排
>
> 各位同學請注意以下考試安排：
>
> | 日期 | 時間 | 科目 | 地點 |
> |------|------|------|------|
> | 6月15日 | 09:00-11:00 | **數學** | 課室A |
> | 6月16日 | 09:00-11:00 | **英文** | 課室B |
>
> **考試須知**：
> 1. 請提前 *15分鐘* 到達考場
> 2. 攜帶 `學生證` 和 `准考證`
> 3. 只能使用 **指定計算器**
>
> ```
> 重要提醒：考試期間請保持安靜
> ```
>
> > 祝各位同學考試順利！ 🍀

### 11.5 引用內的回答題

> **課堂討論題**
>
> 請根據今天學習的內容，回答以下問題：
>
> 1. **概念理解**：什麼是演算法？請用自己的話說明。
>
> ---
> ---
> ---
>
> 2. **實例分析**：請舉出一個日常生活中的演算法例子，並說明其步驟。
>
> ---
> ---
> ---
> ---
>
> 3. **比較分析**：比較以下兩種排序方法的優缺點：
>
> | 排序方法 | 優點 | 缺點 |
> |----------|------|------|
> | 氣泡排序 | | |
> | 快速排序 | | |
>
> 詳細說明：
>
> ---
> ---
> ---

### 11.6 表格內的回答區域

| 題目類型 | 題目內容 | 答案區域 |
|----------|----------|----------|
| **選擇題** | Python 的創始人是誰？<br>A) Guido van Rossum<br>B) Dennis Ritchie<br>C) Bjarne Stroustrup | 答案：_______ |
| **填空題** | Python 是一種 _______ 語言 | --- |
| **簡答題** | 解釋什麼是物件導向程式設計 | ---<br>---<br>--- |
| **計算題** | 計算 2^8 的值 | 計算過程：<br>---<br>答案：_______ |

## 12. 邊界情況測試

### 12.1 特殊字符處理

HTML 特殊字符：< > & " '

程式碼中的特殊字符：`< > & " ' \n \t \r`

數學表達式：`x < y && y > z || a != b`

### 12.2 空白字符處理

這行有多個    空格    在中間。

這行結尾有空格。

	這行開頭有Tab字符。

### 12.3 長文本處理

這是一個非常長的段落，用來測試系統對長文本的處理能力。在實際的教學環境中，經常會遇到包含大量文字的教材內容，系統需要能夠正確地處理這些長文本，保持良好的排版效果和閱讀體驗。這個段落包含了中文和英文的混合內容，以及各種標點符號，用來全面測試文本處理的穩定性和可靠性。

### 12.4 格式嵌套的極限情況

***`這是嵌套的格式：粗體、斜體和程式碼`***

> **引用中的複雜格式**
>
> 1. 列表項目包含 *斜體* 和 **粗體**
>    - 子項目有 `程式碼` 和 ***粗斜體***
>      - 更深層的 ***粗斜體*** 內容
>
> | 表格 | 在引用中 |
> |------|----------|
> | `程式碼` | **粗體** |
> | *斜體* | ***粗斜體*** |

## 13. 教學場景模擬

### 13.1 課程大綱格式

# 📚 程式設計基礎課程

**課程編號**：CS101
**學分**：3學分
**授課教師**：張老師
**上課時間**：星期二、四 14:30-16:00

## 📋 課程目標

完成本課程後，學生將能夠：

1. **理解程式設計的基本概念**
   - 變數與資料型態
   - 控制結構（條件、迴圈）
   - 函數與模組

2. **掌握 Python 程式語言**
   - 基本語法和操作
   - 常用函式庫的使用
   - 除錯和測試技巧

3. **培養解決問題的能力**
   - 分析問題的方法
   - 設計演算法的思維
   - 程式碼優化的技巧

### 13.2 學習目標列表

#### 🎯 知識目標 (Knowledge)
- [ ] 理解程式設計的基本原理
- [ ] 掌握 Python 語法規則
- [ ] 認識常見的資料結構

#### 🛠️ 技能目標 (Skills)
- [ ] 能夠編寫簡單的 Python 程式
- [ ] 能夠除錯和修正程式錯誤
- [ ] 能夠使用開發工具和環境

#### 💭 態度目標 (Attitudes)
- [ ] 培養邏輯思維能力
- [ ] 建立持續學習的習慣
- [ ] 發展團隊合作精神

### 13.3 練習題和答案

**🔢 練習題 1：基礎計算**

編寫一個程式，計算圓的面積。

```python
# 學生作答區域
import math

def calculate_circle_area(radius):
    # 請在此處完成程式碼
    pass

# 測試
radius = 5
area = calculate_circle_area(radius)
print(f"半徑為 {radius} 的圓面積是：{area}")
```

**✅ 參考答案**

```python
import math

def calculate_circle_area(radius):
    """計算圓的面積"""
    return math.pi * radius ** 2

# 測試
radius = 5
area = calculate_circle_area(radius)
print(f"半徑為 {radius} 的圓面積是：{area:.2f}")
```

**🔤 練習題 2：字串處理**

請完成以下填空：

1. 在 Python 中，字串的長度可以用 ________ 函數獲得。
2. 將字串轉換為大寫的方法是 ________。
3. 檢查字串是否以特定字符開頭的方法是 ________。

**✅ 答案**
1. `len()`
2. `.upper()`
3. `.startswith()`

### 13.4 重點提示框

> 💡 **學習提示**
>
> 程式設計是一門實踐性很強的學科，建議：
> - 每天至少練習 30 分鐘
> - 多閱讀他人的程式碼
> - 參與程式設計社群討論

> ⚠️ **常見錯誤**
>
> 初學者容易犯的錯誤：
> 1. 忘記縮排（Python 特有）
> 2. 變數名稱拼寫錯誤
> 3. 混淆 `=` 和 `==` 的用法

> 🎯 **考試重點**
>
> 期末考試將重點考查：
> - **基礎語法**（30%）
> - **程式邏輯**（40%）
> - **實際應用**（30%）

### 13.5 課堂筆記格式

**📅 日期**：2024年12月6日
**📖 主題**：變數與資料型態

#### 🔑 重點概念

**變數 (Variable)**
- 定義：用來儲存資料的容器
- 語法：`變數名 = 值`
- 範例：`name = "張小明"`

**資料型態 (Data Types)**

| 型態 | 英文名稱 | 範例 | 說明 |
|------|----------|------|------|
| 整數 | int | `42` | 沒有小數點的數字 |
| 浮點數 | float | `3.14` | 有小數點的數字 |
| 字串 | str | `"Hello"` | 文字內容 |
| 布林值 | bool | `True` | 真或假 |

#### 📝 課堂練習

```python
# 練習：宣告不同型態的變數
student_name = "李小華"        # 字串
student_age = 16              # 整數
student_height = 165.5        # 浮點數
is_student = True             # 布林值

# 輸出變數內容
print(f"姓名：{student_name}")
print(f"年齡：{student_age}")
print(f"身高：{student_height} 公分")
print(f"是否為學生：{is_student}")
```

#### 💭 課後思考

1. 為什麼需要不同的資料型態？

---
---
---

2. 如何選擇適當的變數名稱？

---
---
---

3. 變數的命名有什麼規則？

---
---
---

#### 📝 課後作業

**作業 1：基礎概念題**

請用自己的話解釋什麼是「變數」，並舉出三個生活中的例子來說明變數的概念。

---
---
---
---

**作業 2：實作練習**

請寫一個簡單的 Python 程式，宣告至少5個不同型態的變數，並輸出它們的值和型態。

程式碼：
```python
# 請在此處寫出你的程式碼

---
---
---
---
---
```

**作業 3：錯誤分析**

以下程式碼有什麼問題？請指出錯誤並提供正確的寫法。

```python
student name = "張小明"
student-age = 16
2grade = "高一"
```

錯誤分析：

---
---
---

正確寫法：

---
---
---

**作業 4：應用題**

假設你要設計一個學生資訊管理系統，需要儲存學生的姓名、年齡、班級、各科成績等資訊。請列出你會使用哪些變數，並說明每個變數的資料型態。

設計說明：

---
---
---
---
---

---

## 14. 數學公式測試（LaTeX 格式）

### 14.1 行內數學公式測試

這是一個簡單的行內公式：$E = mc^2$，以及更複雜的：$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$。

複雜的行內公式：$\sum_{n=1}^{\infty} \frac{1}{n^2} = \frac{\pi^2}{6}$ 和 $\lim_{x \to 0} \frac{\sin x}{x} = 1$。

**基礎數學行內公式**：
- 二次方程式：$ax^2 + bx + c = 0$
- 解的公式：$x = \frac{-b \pm \sqrt{b^2 - 4ac}}{2a}$
- 畢氏定理：$a^2 + b^2 = c^2$
- 歐拉恆等式：$e^{i\pi} + 1 = 0$

**物理公式行內**：
- 牛頓第二定律：$F = ma$
- 動能：$E_k = \frac{1}{2}mv^2$
- 重力位能：$E_p = mgh$
- 歐姆定律：$V = IR$

**化學公式行內**：
- 水的分子式：$H_2O$
- 二氧化碳：$CO_2$
- 硫酸：$H_2SO_4$
- 甲烷：$CH_4$

### 14.2 行中數學公式測試（顯示模式）

**微積分複雜公式**：

多重積分（高斯散度定理）：
$$\iiint_V \nabla \cdot \mathbf{F} \, dV = \iint_{\partial V} \mathbf{F} \cdot \mathbf{n} \, dS$$

拉普拉斯變換：
$$\mathcal{L}\{f(t)\} = F(s) = \int_0^{\infty} f(t) e^{-st} dt$$

傅立葉變換：
$$\mathcal{F}\{f(x)\} = \hat{f}(\xi) = \int_{-\infty}^{\infty} f(x) e^{-2\pi i x \xi} dx$$

**線性代數複雜公式**：

矩陣指數：
$$e^{\mathbf{A}t} = \sum_{n=0}^{\infty} \frac{(\mathbf{A}t)^n}{n!} = \mathbf{I} + \mathbf{A}t + \frac{(\mathbf{A}t)^2}{2!} + \frac{(\mathbf{A}t)^3}{3!} + \cdots$$

複雜矩陣運算：
$$\begin{vmatrix}
a_{11} & a_{12} & \cdots & a_{1n} \\
a_{21} & a_{22} & \cdots & a_{2n} \\
\vdots & \vdots & \ddots & \vdots \\
a_{n1} & a_{n2} & \cdots & a_{nn}
\end{vmatrix} = \sum_{\sigma \in S_n} \text{sgn}(\sigma) \prod_{i=1}^n a_{i,\sigma(i)}$$

**統計學複雜公式**：

多元正態分佈：
$$f(\mathbf{x}) = \frac{1}{(2\pi)^{k/2}|\boldsymbol{\Sigma}|^{1/2}} \exp\left(-\frac{1}{2}(\mathbf{x}-\boldsymbol{\mu})^T\boldsymbol{\Sigma}^{-1}(\mathbf{x}-\boldsymbol{\mu})\right)$$

貝葉斯定理的複雜形式：
$$P(\theta|\mathbf{x}) = \frac{P(\mathbf{x}|\theta)P(\theta)}{\int_{\Theta} P(\mathbf{x}|\theta')P(\theta') d\theta'} = \frac{\mathcal{L}(\theta|\mathbf{x})\pi(\theta)}{m(\mathbf{x})}$$

### 14.3 量子力學公式

薛丁格方程：
$$i\hbar\frac{\partial}{\partial t}\Psi(\mathbf{r},t) = \hat{H}\Psi(\mathbf{r},t) = \left[-\frac{\hbar^2}{2m}\nabla^2 + V(\mathbf{r},t)\right]\Psi(\mathbf{r},t)$$

量子場論：
$$\mathcal{L} = \bar{\psi}(i\gamma^\mu D_\mu - m)\psi - \frac{1}{4}F_{\mu\nu}F^{\mu\nu}$$

其中 $D_\mu = \partial_\mu + ieA_\mu$ 和 $F_{\mu\nu} = \partial_\mu A_\nu - \partial_\nu A_\mu$。

### 14.4 化學公式測試

**基本化學反應**：
$$\ce{2H2 + O2 -> 2H2O}$$

$$\ce{CaCO3 + 2HCl -> CaCl2 + H2O + CO2 ^}$$

**複雜有機化學反應**：
$$\ce{C6H5-CHO + HCN ->[OH-] C6H5-CH(OH)-CN}$$

**生化反應**：
葡萄糖代謝：
$$\ce{C6H12O6 + 6O2 -> 6CO2 + 6H2O + ATP}$$

**配位化合物**：
$$\ce{[Cu(NH3)4]^2+ + 4H2O <=> [Cu(H2O)4]^2+ + 4NH3}$$

### 14.5 複雜分段函數

$$f(x) = \begin{cases}
\frac{\sin(\pi x)}{x} & \text{if } x \neq 0 \\
\pi & \text{if } x = 0 \\
\int_0^x e^{-t^2} dt & \text{if } x > 1 \\
\sum_{n=0}^{\infty} \frac{(-1)^n x^{2n+1}}{(2n+1)!} & \text{if } |x| \leq 1
\end{cases}$$

### 14.6 極度複雜的組合公式

拉馬努金的無窮級數：
$$\frac{1}{\pi} = \frac{2\sqrt{2}}{9801} \sum_{k=0}^{\infty} \frac{(4k)!(1103+26390k)}{(k!)^4 396^{4k}}$$

黎曼ζ函數：
$$\zeta(s) = \sum_{n=1}^{\infty} \frac{1}{n^s} = \prod_{p \text{ prime}} \frac{1}{1-p^{-s}} = \frac{1}{\Gamma(s)} \int_0^{\infty} \frac{t^{s-1}}{e^t - 1} dt$$

### 14.7 物理學複雜公式

愛因斯坦場方程：
$$G_{\mu\nu} + \Lambda g_{\mu\nu} = \frac{8\pi G}{c^4} T_{\mu\nu}$$

其中 $G_{\mu\nu} = R_{\mu\nu} - \frac{1}{2}g_{\mu\nu}R$。

麥克斯韋方程組（張量形式）：
$$\partial_\mu F^{\mu\nu} = \mu_0 J^\nu$$
$$\partial_{[\mu} F_{\nu\rho]} = 0$$

---

## 🎉 測試文件完成

這個綜合測試文件包含了所有要求的 Markdown 元素，特別針對教育材料的需求進行了設計。

### ✅ 測試覆蓋範圍

- ✅ 基礎格式（標題、段落、換行）
- ✅ 文字樣式（粗體、斜體、程式碼）
- ✅ 複雜列表（5層深度嵌套、混合內容）
- ✅ 圖片測試（22張圖片、多種顯示方式）
- ✅ 表格（基本、對齊、格式化、內嵌回答區）
- ✅ 程式碼（行內、區塊、語言指定）
- ✅ 引用（單層、多層、混合格式、內嵌回答題）
- ✅ 線條（填空線、水平分隔線、回答題橫線）
- ✅ 教育元素（重點標記、練習題、回答題）
- ✅ 中文內容（繁體、混排、教育術語）
- ✅ 複雜組合（表格+列表、引用+程式碼+回答題）
- ✅ 邊界情況（特殊字符、長文本）
- ✅ 教學場景（課程大綱、筆記格式、作業設計）
- ✅ LaTeX 公式（行內+行中、數學、化學、物理）
- ✅ 回答題格式（3-4條橫線、多種題型）

### 🎯 使用建議

1. **上傳測試**：將此文件上傳到系統進行轉換
2. **逐項檢查**：按照各個章節檢查轉換效果
3. **重點關注**：中文顯示、表格格式、程式碼高亮
4. **教育功能**：填空線、重點標記的顯示效果

這個測試文件將幫助您全面驗證週 1 的 Markdown → HTML 轉換功能！

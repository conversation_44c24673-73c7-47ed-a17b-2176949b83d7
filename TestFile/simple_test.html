<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>simple_test.md</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #fff;
        }
        
        h1, h2, h3, h4, h5, h6 {
            margin-top: 24px;
            margin-bottom: 16px;
            font-weight: 600;
            line-height: 1.25;
        }
        
        h1 { font-size: 2em; border-bottom: 1px solid #eaecef; padding-bottom: 10px; }
        h2 { font-size: 1.5em; border-bottom: 1px solid #eaecef; padding-bottom: 8px; }
        h3 { font-size: 1.25em; }
        
        p { margin-bottom: 16px; }
        
        img {
            max-width: 100%;
            height: auto;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin: 10px 0;
        }
        
        code {
            background-color: #f6f8fa;
            border-radius: 3px;
            font-size: 85%;
            margin: 0;
            padding: 0.2em 0.4em;
        }
        
        pre {
            background-color: #f6f8fa;
            border-radius: 6px;
            font-size: 85%;
            line-height: 1.45;
            overflow: auto;
            padding: 16px;
        }
        
        blockquote {
            border-left: 4px solid #dfe2e5;
            margin: 0;
            padding: 0 16px;
            color: #6a737d;
        }
        
        ul, ol {
            padding-left: 30px;
        }
        
        li {
            margin-bottom: 4px;
        }
        
        /* 表格樣式已在內容中定義 */
        
        /* Checkbox 樣式 */
        input[type="checkbox"] {
            margin-right: 8px;
        }
        
        /* 填空線和回答線樣式已在內容中定義 */
        
        .metadata {
            background-color: #f8f9fa;
            border: 1px solid #e1e4e8;
            border-radius: 6px;
            padding: 16px;
            margin: 20px 0;
            font-size: 14px;
            color: #586069;
        }
    </style>
</head>
<body>
    <div class="metadata">
        <strong>📊 文檔信息</strong><br>
        生成時間: N/A秒 | 
        字數: 59 | 
        字符數: 358 | 
        行數: 38
        
        
    </div>
    
    
        <!DOCTYPE html>
        <html lang="zh-TW">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>教育材料 - 週 1 轉換結果</title>
            
        <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
                line-height: 1.6;
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
                color: #333;
                background-color: #fff;
            }
            
            h1, h2, h3, h4, h5, h6 {
                color: #2c3e50;
                margin-top: 1.5em;
                margin-bottom: 0.5em;
            }
            
            h1 { font-size: 2em; border-bottom: 2px solid #3498db; padding-bottom: 0.3em; }
            h2 { font-size: 1.5em; border-bottom: 1px solid #bdc3c7; padding-bottom: 0.2em; }
            h3 { font-size: 1.3em; }
            
            p { margin: 1em 0; }
            
            ul, ol {
                margin: 1em 0;
                padding-left: 2em;
            }
            
            li { margin: 0.5em 0; }
            
            blockquote {
                border-left: 4px solid #3498db;
                margin: 1em 0;
                padding: 0.5em 1em;
                background-color: #f8f9fa;
                color: #555;
            }
            
            code {
                background-color: #f1f2f6;
                padding: 0.2em 0.4em;
                border-radius: 3px;
                font-family: 'Courier New', Consolas, monospace;
                font-size: 0.9em;
            }
            
            pre {
                background-color: #f8f9fa;
                border: 1px solid #e9ecef;
                border-radius: 4px;
                padding: 1em;
                overflow-x: auto;
                margin: 1em 0;
            }
            
            pre code {
                background-color: transparent;
                padding: 0;
            }
            
            table {
                border-collapse: collapse;
                width: 100%;
                margin: 1em 0;
            }
            
            th, td {
                border: 1px solid #ddd;
                padding: 0.5em;
                text-align: left;
            }
            
            th {
                background-color: #f2f2f2;
                font-weight: bold;
            }
            
            strong { color: #2c3e50; }
            em { color: #7f8c8d; }
            
            /* 週 1 特殊標記 */
            .week1-processed {
                border-top: 3px solid #27ae60;
                padding-top: 1em;
                margin-top: 2em;
            }
            
            .week1-footer {
                margin-top: 2em;
                padding-top: 1em;
                border-top: 1px solid #ecf0f1;
                font-size: 0.9em;
                color: #7f8c8d;
                text-align: center;
            }
        </style>
        
        </head>
        <body>
            <div class="week1-processed">
                
<!-- MathJax 3.0 配置 - 離線友好版本 -->
<script>
window.MathJax = {
  tex: {
    inlineMath: [['$', '$']],
    displayMath: [['$$', '$$']],
    processEscapes: true,
    processEnvironments: true
  },
  startup: {
    ready: () => {
      console.log('🔢 MathJax 3.0 正在初始化...');
      MathJax.startup.defaultReady();
    },
    pageReady: () => {
      console.log('🔢 MathJax 頁面準備完成，開始渲染數學公式...');
      return MathJax.startup.defaultPageReady().then(() => {
        console.log('🔢 MathJax 渲染完成！');
      });
    }
  }
};
</script>
<!-- 使用更可靠的 CDN -->
<script id="MathJax-script" src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"
        onerror="console.log('⚠️ MathJax CDN 載入失敗，數學公式可能無法顯示')"></script>

<h1>簡單測試文檔</h1>
<h2>數學公式測試</h2>
<p>這是愛因斯坦公式：$E = mc^2$</p>
<p>這是積分公式：
$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$</p>
<h2>圖片測試</h2>
<p><img src="photo/Gemini_Generated_Image_1.png" alt="測試圖片1" /></p>
<h2>表格測試</h2>
<table class="edu-table" style="
            border-collapse: collapse;
            width: 100%;
            margin: 1em 0;
            border: 1px solid #ddd;
            font-size: 14px;
        ">
<thead>
<tr>
<th style="
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
            font-weight: bold;
        ">科目</th>
<th style="
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
            font-weight: bold;
        ">分數</th>
<th style="
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            padding: 8px 12px;
            text-align: left;
            font-weight: bold;
        ">等級</th>
</tr>
</thead>
<tbody>
<tr>
<td style="
            border: 1px solid #ddd;
            padding: 8px 12px;
            vertical-align: top;
        ">數學</td>
<td style="
            border: 1px solid #ddd;
            padding: 8px 12px;
            vertical-align: top;
        ">95</td>
<td style="
            border: 1px solid #ddd;
            padding: 8px 12px;
            vertical-align: top;
        ">A+</td>
</tr>
<tr>
<td style="
            border: 1px solid #ddd;
            padding: 8px 12px;
            vertical-align: top;
        ">物理</td>
<td style="
            border: 1px solid #ddd;
            padding: 8px 12px;
            vertical-align: top;
        ">88</td>
<td style="
            border: 1px solid #ddd;
            padding: 8px 12px;
            vertical-align: top;
        ">A</td>
</tr>
</tbody>
</table>
<h2>任務列表測試</h2>
<ul class="contains-task-list">
<li class="task-list-item"><input class="task-list-item-checkbox" checked="checked" disabled="disabled" type="checkbox"> 完成數學作業</li>
<li class="task-list-item"><input class="task-list-item-checkbox" disabled="disabled" type="checkbox"> 準備物理考試</li>
</ul>
<h2>填空題測試</h2>
<p>請填寫：愛因斯坦的質能方程是 <span class="fill-blank" style="border-bottom: 2px solid #333; min-width: 100px; display: inline-block;">&nbsp;&nbsp;&nbsp;&nbsp;</span></p>
<h2>回答題測試</h2>
<p>請解釋相對論的基本概念：</p>
<hr />
<hr />
<hr />

            </div>
            <div class="week1-footer">
                📚 教育材料轉換系統 - 週 1 基礎版本 | 生成時間: 2025-06-07 10:03:24
            </div>
        </body>
        </html>
        
    
    <div class="metadata" style="margin-top: 40px;">
        <strong>🔧 生成工具</strong><br>
        由 Markdown 轉換工具生成 | 解析器: markdown-it-py | 版本: Week 1
    </div>
</body>
</html>
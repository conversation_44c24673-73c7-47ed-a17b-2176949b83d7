# 教育材料轉換系統 - 綜合測試文件

這是一個全面的 Markdown 測試文件，用於驗證教育材料轉換系統的各項功能。

## 1. 基礎格式測試

### 1.1 標題層級測試

# 一級標題 - 課程主題
## 二級標題 - 章節標題  
### 三級標題 - 小節標題
#### 四級標題 - 重點內容
##### 五級標題 - 詳細說明
###### 六級標題 - 補充資料

### 1.2 文字格式測試

**粗體文字** - 重要概念  
*斜體文字* - 強調內容  
***粗斜體文字*** - 特別重要  
~~刪除線文字~~ - 已修正內容  
`行內代碼` - 程式碼片段  

### 1.3 段落和換行測試

這是第一段文字，包含多個句子。這裡測試段落的正常顯示效果。

這是第二段文字，  
這裡測試強制換行效果。

> 這是引用文字，通常用於重要提醒或引用他人觀點。
> 
> 引用可以包含多個段落。

## 2. 列表測試

### 2.1 無序列表

- 第一項內容
- 第二項內容
  - 子項目 A
  - 子項目 B
    - 更深層的子項目
- 第三項內容

### 2.2 有序列表

1. 第一步驟
2. 第二步驟
   1. 子步驟 A
   2. 子步驟 B
      1. 詳細步驟 1
      2. 詳細步驟 2
3. 第三步驟

### 2.3 任務列表 (Checkbox)

- [x] 已完成的任務
- [x] 另一個已完成的任務
- [ ] 未完成的任務
- [ ] 待處理的項目
- [x] 重要的已完成項目

## 3. 數學公式測試

### 3.1 行內數學公式

這是一個簡單的數學公式：$E = mc^2$

計算圓的面積：$A = \pi r^2$

二次方程式：$ax^2 + bx + c = 0$

### 3.2 行中數學公式

$$\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}$$

$$\sum_{i=1}^{n} i = \frac{n(n+1)}{2}$$

$$\lim_{x \to 0} \frac{\sin x}{x} = 1$$

### 3.3 複雜數學公式

$$\frac{\partial^2 u}{\partial t^2} = c^2 \nabla^2 u$$

$$\begin{pmatrix}
a & b \\
c & d
\end{pmatrix}
\begin{pmatrix}
x \\
y
\end{pmatrix}
=
\begin{pmatrix}
ax + by \\
cx + dy
\end{pmatrix}$$

## 4. 圖片測試

### 4.1 基本圖片顯示

**第一組圖片：**
![圖片1](photo/Gemini_Generated_Image_1.png)
![圖片2](photo/Gemini_Generated_Image_2.png)
![圖片3](photo/Gemini_Generated_Image_3.png)

**第二組圖片（重複測試）：**
![教學圖片A](photo/Gemini_Generated_Image_1.png)
![教學圖片B](photo/Gemini_Generated_Image_2.png)
![教學圖片C](photo/Gemini_Generated_Image_3.png)

**第三組圖片（帶說明）：**
![示意圖1](photo/Gemini_Generated_Image_1.png "第一張示意圖")
![示意圖2](photo/Gemini_Generated_Image_2.png "第二張示意圖")
![示意圖3](photo/Gemini_Generated_Image_3.png "第三張示意圖")

### 4.2 圖片與文字混排測試

這是一段文字，後面跟著圖片：![內嵌圖片](photo/Gemini_Generated_Image_1.png)

**圖片說明測試：**

![教學流程圖](photo/Gemini_Generated_Image_1.png "教學流程示意圖")
*圖 1：教學流程示意圖*

![學習架構圖](photo/Gemini_Generated_Image_2.png "學習架構示意圖")
*圖 2：學習架構示意圖*

![評估方法圖](photo/Gemini_Generated_Image_3.png "評估方法示意圖")
*圖 3：評估方法示意圖*

### 4.3 多張圖片連續顯示

連續多張圖片：
![測試1](photo/Gemini_Generated_Image_1.png)
![測試2](photo/Gemini_Generated_Image_2.png)
![測試3](photo/Gemini_Generated_Image_3.png)
![測試4](photo/Gemini_Generated_Image_1.png)
![測試5](photo/Gemini_Generated_Image_2.png)
![測試6](photo/Gemini_Generated_Image_3.png)
![測試7](photo/Gemini_Generated_Image_1.png)
![測試8](photo/Gemini_Generated_Image_2.png)
![測試9](photo/Gemini_Generated_Image_3.png)

## 5. 表格測試

### 5.1 基本表格

| 學生姓名 | 數學成績 | 英語成績 | 總分 |
|----------|----------|----------|------|
| 張小明   | 85       | 92       | 177  |
| 李小華   | 78       | 88       | 166  |
| 王小美   | 92       | 85       | 177  |
| 陳小強   | 88       | 90       | 178  |

### 5.2 對齊表格

| 左對齊 | 居中對齊 | 右對齊 |
|:-------|:--------:|-------:|
| 內容A  |   內容B  |  內容C |
| 較長的內容 | 中等內容 | 短內容 |
| 測試   |   測試   |   測試 |

### 5.3 複雜表格

| 課程名稱 | 學分 | 授課教師 | 上課時間 | 教室 |
|----------|------|----------|----------|------|
| 高等數學 | 4    | 張教授   | 週一 9:00-11:00 | A101 |
| 程式設計 | 3    | 李教授   | 週二 14:00-16:00 | B205 |
| 資料結構 | 3    | 王教授   | 週三 10:00-12:00 | C301 |
| 演算法   | 3    | 陳教授   | 週四 15:00-17:00 | D402 |

## 6. 代碼測試

### 6.1 行內代碼

使用 `print()` 函數來輸出結果。
變數 `x = 10` 儲存數值。
執行 `git commit -m "更新"` 提交變更。

### 6.2 代碼區塊

```python
def calculate_area(radius):
    """計算圓的面積"""
    import math
    return math.pi * radius ** 2

# 測試函數
radius = 5
area = calculate_area(radius)
print(f"半徑為 {radius} 的圓面積是 {area:.2f}")
```

```javascript
function fibonacci(n) {
    if (n <= 1) return n;
    return fibonacci(n - 1) + fibonacci(n - 2);
}

// 計算前10個費波那契數列
for (let i = 0; i < 10; i++) {
    console.log(`F(${i}) = ${fibonacci(i)}`);
}
```

## 7. 連結測試

### 7.1 基本連結

[Google 搜尋](https://www.google.com)
[GitHub](https://github.com)
[維基百科](https://zh.wikipedia.org)

### 7.2 參考式連結

這是一個 [參考連結][1] 的例子。
另一個 [連結][google] 指向 Google。

[1]: https://www.example.com
[google]: https://www.google.com

## 8. 教育專用功能測試

### 8.1 填空題

請填入正確答案：

1. 地球的衛星是 ________。
2. 水的化學式是 ________。
3. 中華民國的首都是 ________。

### 8.2 回答題

請回答以下問題：

**問題 1：** 請說明光合作用的過程。

---

**問題 2：** 計算 15 × 23 的結果。

---

**問題 3：** 列舉三個再生能源的例子。

---

### 8.3 選擇題

**1. 下列何者是質數？**
- [ ] A. 4
- [x] B. 7
- [ ] C. 9
- [ ] D. 12

**2. 台灣最高的山是？**
- [ ] A. 雪山
- [x] B. 玉山
- [ ] C. 阿里山
- [ ] D. 合歡山

## 9. 特殊符號和格式

### 9.1 特殊符號

© 版權符號  
® 註冊商標  
™ 商標符號  
° 度數符號  
± 正負號  
× 乘號  
÷ 除號  
≤ 小於等於  
≥ 大於等於  
≠ 不等於  

### 9.2 分隔線

---

***

___

## 10. 總結

這個測試文件包含了：

- ✅ 6 個層級的標題
- ✅ 各種文字格式
- ✅ 3 種類型的列表
- ✅ 22 張圖片測試
- ✅ 複雜的數學公式
- ✅ 多種表格格式
- ✅ 代碼區塊
- ✅ 連結測試
- ✅ 教育專用功能
- ✅ 特殊符號

**測試完成時間：** 2025年6月6日  
**文件版本：** v2.0  
**測試狀態：** 🟢 準備就緒
